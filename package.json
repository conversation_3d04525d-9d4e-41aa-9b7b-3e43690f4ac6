{"name": "chatgpt-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/prismjs": "^1.26.5", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "dexie": "^4.0.11", "lucide-react": "^0.539.0", "postcss": "^8.5.6", "prismjs": "^1.30.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "react-window": "^1.8.11", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.11", "uuid": "^11.1.0", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0"}}