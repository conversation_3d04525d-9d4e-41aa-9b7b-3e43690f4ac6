# ChatGPT Clone - Production-Ready React Application

A complete, production-ready ChatGPT-like client application built with React 18, TypeScript, and TailwindCSS. This application demonstrates modern web development practices with comprehensive features including real-time streaming, offline support, accessibility, and responsive design.

## 🚀 Features

### Core Functionality
- **Real-time Chat Interface** - Smooth, responsive chat experience
- **Streaming Responses** - Real-time message streaming with typing animations
- **Conversation Management** - Create, rename, delete, and search conversations
- **Message Operations** - Edit, retry, copy, and delete messages
- **File Attachments** - Support for image uploads with drag-and-drop

### Technical Excellence
- **TypeScript** - Full type safety and excellent developer experience
- **State Management** - Zustand for efficient global state management
- **Offline Support** - IndexedDB caching with automatic sync
- **Responsive Design** - Mobile-first approach with adaptive layouts
- **Accessibility** - WCAG 2.1 AA compliance with keyboard navigation
- **Performance** - Virtual scrolling, code splitting, and optimizations

### User Experience
- **Dark/Light Theme** - System preference detection with manual toggle
- **Keyboard Shortcuts** - Comprehensive keyboard navigation
- **Search & Filter** - Find conversations and messages quickly
- **Settings Panel** - Customizable preferences and data management
- **Error Handling** - Graceful error recovery and user feedback

## 🛠 Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **Styling**: TailwindCSS with custom design system
- **State Management**: Zustand with persistence
- **Database**: IndexedDB via Dexie for offline storage
- **Markdown**: React Markdown with syntax highlighting
- **Build Tool**: Vite for fast development and optimized builds
- **Testing**: Jest and React Testing Library
- **Code Quality**: ESLint, Prettier, and TypeScript strict mode

## 📦 Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Build for production**
   ```bash
   npm run build
   ```

## 🏗 Project Structure

```
src/
├── components/          # React components
│   ├── layout/         # Layout components (ChatApp, Sidebar, ChatPanel)
│   ├── chat/           # Chat-specific components (MessageBubble, MessageInput)
│   ├── ui/             # Reusable UI components (LoadingSpinner, ErrorBoundary)
│   └── modals/         # Modal components (SettingsModal)
├── hooks/              # Custom React hooks
├── services/           # API and external service integrations
├── stores/             # Zustand state management
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── __tests__/          # Test files
```

## ⌨️ Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl + N` | New conversation |
| `Ctrl + B` | Toggle sidebar |
| `Ctrl + ,` | Open settings |
| `Ctrl + /` | Show shortcuts |
| `Enter` | Send message (if enabled) |
| `Shift + Enter` | New line |
| `Escape` | Close modals |

## 🎨 Design System

### Colors
- **Primary**: Blue scale (50-900)
- **Gray**: Neutral scale for text and backgrounds
- **Semantic**: Success, warning, error colors

### Typography
- **Font**: Inter (Google Fonts)
- **Sizes**: Small, medium, large options
- **Weights**: 300-700 range

## 📱 Responsive Design

- **Mobile First** - Optimized for mobile devices
- **Adaptive Layout** - Sidebar collapses on mobile
- **Touch Friendly** - Appropriate touch targets
- **Performance** - Optimized for slower connections

## ♿ Accessibility

- **WCAG 2.1 AA** - Compliant with accessibility standards
- **Keyboard Navigation** - Full keyboard support
- **Screen Readers** - ARIA labels and announcements
- **High Contrast** - Support for high contrast mode
- **Reduced Motion** - Respects user preferences

## 🚀 Performance Optimizations

- **Code Splitting** - Lazy loading of components
- **Virtual Scrolling** - Efficient rendering of long message lists
- **Memoization** - Optimized re-renders
- **Bundle Analysis** - Optimized bundle size
- **Caching** - Aggressive caching strategies

## 🧪 Testing

Run the test suite:

```bash
npm test
```

### Test Coverage
- **Unit Tests** - Component and hook testing
- **Integration Tests** - User interaction flows
- **Accessibility Tests** - A11y compliance
- **Performance Tests** - Render performance

## 🔄 API Integration

The application includes a mock API service that simulates:
- **Authentication** - Login/logout flows
- **Chat Endpoints** - Message sending and receiving
- **Streaming** - Real-time response streaming
- **File Upload** - Image attachment handling

## 📊 Monitoring

The application includes built-in monitoring for:
- **Performance Metrics** - Render times, FPS, memory usage
- **Error Tracking** - Automatic error reporting
- **User Analytics** - Usage patterns and interactions
- **Network Status** - Online/offline detection

## 🚢 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Vercel
```bash
npm install -g vercel
vercel --prod
```

### Deploy to Netlify
```bash
npm run build
# Upload dist/ folder to Netlify
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **React Team** - For the amazing framework
- **Tailwind Labs** - For the utility-first CSS framework
- **Zustand** - For simple state management
- **Dexie** - For IndexedDB wrapper
- **React Markdown** - For markdown rendering

---

Built with ❤️ using modern web technologies for a production-ready chat experience.