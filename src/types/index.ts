// Core message and conversation types
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'error';
  attachments?: File[];
  conversation_id?: string; // For storage purposes
  metadata?: {
    model?: string;
    tokens?: number;
    cost?: number;
  };
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  created_at: Date;
  updated_at: Date;
  model?: string;
  metadata?: {
    totalTokens?: number;
    totalCost?: number;
  };
}

// User preferences and settings
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  model: string;
  font_size: 'small' | 'medium' | 'large';
  send_on_enter: boolean;
  show_timestamps: boolean;
  auto_scroll: boolean;
  sound_enabled: boolean;
  compact_mode: boolean;
}

// API related types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ChatRequest {
  message: string;
  conversation_id?: string;
  model?: string;
  attachments?: File[];
  stream?: boolean;
}

export interface ChatResponse {
  message: Message;
  conversation_id: string;
}

export interface StreamChunk {
  id: string;
  content: string;
  done: boolean;
  conversation_id: string;
}

// UI State types
export interface UIState {
  sidebarOpen: boolean;
  settingsOpen: boolean;
  loading: boolean;
  error: string | null;
  currentConversationId: string | null;
  isStreaming: boolean;
  streamingMessageId: string | null;
}

// Authentication types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  preferences: UserPreferences;
  subscription?: {
    plan: 'free' | 'pro' | 'enterprise';
    expires_at?: Date;
  };
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// File upload types
export interface FileUpload {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

// Search and filter types
export interface SearchFilters {
  query: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  model?: string;
  hasAttachments?: boolean;
}

// Component prop types
export interface MessageBubbleProps {
  message: Message;
  isStreaming?: boolean;
  onRetry?: () => void;
  onEdit?: (content: string) => void;
  onDelete?: () => void;
}

export interface ConversationItemProps {
  conversation: Conversation;
  isActive: boolean;
  onSelect: () => void;
  onRename: (title: string) => void;
  onDelete: () => void;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Storage types
export interface StorageAdapter {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
}

// Event types
export interface ChatEvent {
  type: 'message_sent' | 'message_received' | 'conversation_created' | 'conversation_updated' | 'error';
  payload: any;
  timestamp: Date;
}

// Keyboard shortcut types
export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: string;
  description: string;
}

// Theme types
export interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    success: string;
    warning: string;
  };
}

// Model configuration types
export interface ModelConfig {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  costPer1kTokens: number;
  supportsStreaming: boolean;
  supportsImages: boolean;
  provider: 'openai' | 'anthropic' | 'google' | 'local';
}

// Export utility types
export type MessageRole = Message['role'];
export type MessageStatus = Message['status'];
export type Theme = UserPreferences['theme'];
export type FontSize = UserPreferences['font_size'];
export type SubscriptionPlan = NonNullable<User['subscription']>['plan'];

// Generic utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Event handler types
export type EventHandler<T = any> = (event: T) => void;
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;

// Component ref types
export interface ChatInputRef {
  focus: () => void;
  clear: () => void;
  insertText: (text: string) => void;
}

export interface ChatPanelRef {
  scrollToBottom: () => void;
  scrollToMessage: (messageId: string) => void;
}
