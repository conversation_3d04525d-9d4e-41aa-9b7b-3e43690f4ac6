import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { Conversation, Message } from '../types';

interface ConversationState {
  conversations: Conversation[];
  currentConversationId: string | null;
  isLoading: boolean;
  error: string | null;
}

interface ConversationActions {
  // Conversation management
  createConversation: (title?: string) => string;
  deleteConversation: (id: string) => void;
  updateConversation: (id: string, updates: Partial<Conversation>) => void;
  setCurrentConversation: (id: string | null) => void;
  getCurrentConversation: () => Conversation | null;
  
  // Message management
  addMessage: (conversationId: string, message: Omit<Message, 'id' | 'timestamp'>) => string;
  updateMessage: (conversationId: string, messageId: string, updates: Partial<Message>) => void;
  deleteMessage: (conversationId: string, messageId: string) => void;
  
  // Utility actions
  clearAllConversations: () => void;
  searchConversations: (query: string) => Conversation[];
  getConversationById: (id: string) => Conversation | null;
  
  // Loading and error states
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

type ConversationStore = ConversationState & ConversationActions;

export const useConversationStore = create<ConversationStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        conversations: [],
        currentConversationId: null,
        isLoading: false,
        error: null,

        // Conversation management
        createConversation: (title = 'New Chat') => {
          const id = uuidv4();
          const newConversation: Conversation = {
            id,
            title,
            messages: [],
            created_at: new Date(),
            updated_at: new Date(),
          };

          set((state) => ({
            conversations: [newConversation, ...state.conversations],
            currentConversationId: id,
            error: null,
          }));

          return id;
        },

        deleteConversation: (id: string) => {
          set((state) => ({
            conversations: state.conversations.filter((conv) => conv.id !== id),
            currentConversationId: 
              state.currentConversationId === id ? null : state.currentConversationId,
          }));
        },

        updateConversation: (id: string, updates: Partial<Conversation>) => {
          set((state) => ({
            conversations: state.conversations.map((conv) =>
              conv.id === id
                ? { ...conv, ...updates, updated_at: new Date() }
                : conv
            ),
          }));
        },

        setCurrentConversation: (id: string | null) => {
          set({ currentConversationId: id });
        },

        getCurrentConversation: () => {
          const { conversations, currentConversationId } = get();
          return conversations.find((conv) => conv.id === currentConversationId) || null;
        },

        // Message management
        addMessage: (conversationId: string, messageData: Omit<Message, 'id' | 'timestamp'>) => {
          const messageId = uuidv4();
          const message: Message = {
            ...messageData,
            id: messageId,
            timestamp: new Date(),
          };

          set((state) => ({
            conversations: state.conversations.map((conv) =>
              conv.id === conversationId
                ? {
                    ...conv,
                    messages: [...conv.messages, message],
                    updated_at: new Date(),
                    // Auto-generate title from first user message
                    title: conv.messages.length === 0 && message.role === 'user'
                      ? message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
                      : conv.title,
                  }
                : conv
            ),
          }));

          return messageId;
        },

        updateMessage: (conversationId: string, messageId: string, updates: Partial<Message>) => {
          set((state) => ({
            conversations: state.conversations.map((conv) =>
              conv.id === conversationId
                ? {
                    ...conv,
                    messages: conv.messages.map((msg) =>
                      msg.id === messageId ? { ...msg, ...updates } : msg
                    ),
                    updated_at: new Date(),
                  }
                : conv
            ),
          }));
        },

        deleteMessage: (conversationId: string, messageId: string) => {
          set((state) => ({
            conversations: state.conversations.map((conv) =>
              conv.id === conversationId
                ? {
                    ...conv,
                    messages: conv.messages.filter((msg) => msg.id !== messageId),
                    updated_at: new Date(),
                  }
                : conv
            ),
          }));
        },

        // Utility actions
        clearAllConversations: () => {
          set({
            conversations: [],
            currentConversationId: null,
            error: null,
          });
        },

        searchConversations: (query: string) => {
          const { conversations } = get();
          const lowercaseQuery = query.toLowerCase();
          
          return conversations.filter((conv) =>
            conv.title.toLowerCase().includes(lowercaseQuery) ||
            conv.messages.some((msg) =>
              msg.content.toLowerCase().includes(lowercaseQuery)
            )
          );
        },

        getConversationById: (id: string) => {
          const { conversations } = get();
          return conversations.find((conv) => conv.id === id) || null;
        },

        // Loading and error states
        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error: string | null) => {
          set({ error });
        },
      }),
      {
        name: 'conversation-store',
        partialize: (state) => ({
          conversations: state.conversations,
          currentConversationId: state.currentConversationId,
        }),
      }
    ),
    {
      name: 'conversation-store',
    }
  )
);
