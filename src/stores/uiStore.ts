import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { UIState } from '../types';

interface UIActions {
  // Sidebar management
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  
  // Modal management
  toggleSettings: () => void;
  setSettingsOpen: (open: boolean) => void;
  
  // Loading states
  setLoading: (loading: boolean) => void;
  
  // Error handling
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Streaming states
  setStreaming: (isStreaming: boolean, messageId?: string | null) => void;
  
  // Mobile responsiveness
  setMobileView: (isMobile: boolean) => void;
  
  // Theme management
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleTheme: () => void;
  
  // Keyboard shortcuts
  setShortcutsVisible: (visible: boolean) => void;
  
  // Search
  setSearchQuery: (query: string) => void;
  setSearchVisible: (visible: boolean) => void;
}

interface UIStoreState extends UIState {
  // Additional UI state
  isMobile: boolean;
  theme: 'light' | 'dark' | 'system';
  shortcutsVisible: boolean;
  searchQuery: string;
  searchVisible: boolean;
}

type UIStore = UIStoreState & UIActions;

export const useUIStore = create<UIStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      sidebarOpen: true,
      settingsOpen: false,
      loading: false,
      error: null,
      currentConversationId: null,
      isStreaming: false,
      streamingMessageId: null,
      isMobile: false,
      theme: 'system',
      shortcutsVisible: false,
      searchQuery: '',
      searchVisible: false,

      // Sidebar management
      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }));
      },

      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open });
      },

      // Modal management
      toggleSettings: () => {
        set((state) => ({ settingsOpen: !state.settingsOpen }));
      },

      setSettingsOpen: (open: boolean) => {
        set({ settingsOpen: open });
      },

      // Loading states
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // Error handling
      setError: (error: string | null) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      // Streaming states
      setStreaming: (isStreaming: boolean, messageId: string | null = null) => {
        set({ 
          isStreaming, 
          streamingMessageId: isStreaming ? messageId : null 
        });
      },

      // Mobile responsiveness
      setMobileView: (isMobile: boolean) => {
        set({ 
          isMobile,
          // Auto-close sidebar on mobile when not needed
          sidebarOpen: isMobile ? false : get().sidebarOpen,
        });
      },

      // Theme management
      setTheme: (theme: 'light' | 'dark' | 'system') => {
        set({ theme });
        
        // Apply theme to document
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else if (theme === 'light') {
          root.classList.remove('dark');
        } else {
          // System theme
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          if (prefersDark) {
            root.classList.add('dark');
          } else {
            root.classList.remove('dark');
          }
        }
      },

      toggleTheme: () => {
        const { theme } = get();
        const newTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light';
        get().setTheme(newTheme);
      },

      // Keyboard shortcuts
      setShortcutsVisible: (visible: boolean) => {
        set({ shortcutsVisible: visible });
      },

      // Search
      setSearchQuery: (query: string) => {
        set({ searchQuery: query });
      },

      setSearchVisible: (visible: boolean) => {
        set({ searchVisible: visible });
      },
    }),
    {
      name: 'ui-store',
    }
  )
);

// Initialize theme on store creation
if (typeof window !== 'undefined') {
  const store = useUIStore.getState();
  store.setTheme(store.theme);
  
  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  mediaQuery.addEventListener('change', () => {
    const currentTheme = useUIStore.getState().theme;
    if (currentTheme === 'system') {
      store.setTheme('system');
    }
  });
  
  // Listen for window resize for mobile detection
  const handleResize = () => {
    const isMobile = window.innerWidth < 768;
    store.setMobileView(isMobile);
  };
  
  handleResize(); // Initial check
  window.addEventListener('resize', handleResize);
}
