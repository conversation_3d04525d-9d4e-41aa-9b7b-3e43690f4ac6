import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { User, UserPreferences, AuthState } from '../types';

interface UserActions {
  // Authentication
  login: (user: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  
  // Preferences
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  
  // Loading and error states
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

type UserStore = AuthState & UserActions;

const defaultPreferences: UserPreferences = {
  theme: 'system',
  model: 'gpt-4',
  font_size: 'medium',
  send_on_enter: true,
  show_timestamps: false,
  auto_scroll: true,
  sound_enabled: false,
  compact_mode: false,
};

export const useUserStore = create<UserStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // Authentication
        login: (user: User) => {
          set({
            user: {
              ...user,
              preferences: { ...defaultPreferences, ...user.preferences },
            },
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        },

        logout: () => {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        },

        updateUser: (updates: Partial<User>) => {
          const { user } = get();
          if (user) {
            set({
              user: { ...user, ...updates },
            });
          }
        },

        // Preferences
        updatePreferences: (preferences: Partial<UserPreferences>) => {
          const { user } = get();
          if (user) {
            const updatedPreferences = { ...user.preferences, ...preferences };
            set({
              user: {
                ...user,
                preferences: updatedPreferences,
              },
            });
          }
        },

        resetPreferences: () => {
          const { user } = get();
          if (user) {
            set({
              user: {
                ...user,
                preferences: defaultPreferences,
              },
            });
          }
        },

        // Loading and error states
        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error: string | null) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: 'user-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'user-store',
    }
  )
);

// Create a mock user for development
export const createMockUser = (): User => ({
  id: 'mock-user-1',
  email: '<EMAIL>',
  name: 'Demo User',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
  preferences: defaultPreferences,
  subscription: {
    plan: 'pro',
    expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
  },
});
