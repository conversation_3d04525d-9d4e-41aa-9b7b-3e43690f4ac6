import <PERSON><PERSON>, { Table } from 'dexie';
import { Conversation, Message, UserPreferences } from '../types';

// IndexedDB database for offline storage
export class ChatDatabase extends <PERSON>ie {
  conversations!: Table<Conversation>;
  messages!: Table<Message>;
  preferences!: Table<{ id: string; data: UserPreferences }>;

  constructor() {
    super('ChatDatabase');

    this.version(1).stores({
      conversations: 'id, title, created_at, updated_at, model',
      messages: 'id, role, timestamp, status, conversation_id',
      preferences: 'id',
    });
  }
}

export const db = new ChatDatabase();

// Storage service for managing offline data
export class StorageService {
  // Conversation storage
  static async saveConversation(conversation: Conversation): Promise<void> {
    try {
      await db.conversations.put(conversation);

      // Save messages separately for better querying
      if (conversation.messages.length > 0) {
        const messagesWithConvId = conversation.messages.map(msg => ({
          ...msg,
          conversation_id: conversation.id,
        }));
        await db.messages.bulkPut(messagesWithConvId);
      }
    } catch (error) {
      console.error('Failed to save conversation:', error);
      throw error;
    }
  }

  static async getConversation(id: string): Promise<Conversation | null> {
    try {
      const conversation = await db.conversations.get(id);
      if (!conversation) return null;

      const messages = await db.messages
        .where('conversation_id')
        .equals(id)
        .sortBy('timestamp');

      return {
        ...conversation,
        messages,
      };
    } catch (error) {
      console.error('Failed to get conversation:', error);
      return null;
    }
  }

  static async getAllConversations(): Promise<Conversation[]> {
    try {
      const conversations = await db.conversations
        .orderBy('updated_at')
        .reverse()
        .toArray();

      // Load messages for each conversation
      const conversationsWithMessages = await Promise.all(
        conversations.map(async (conv) => {
          const messages = await db.messages
            .where('conversation_id')
            .equals(conv.id)
            .sortBy('timestamp');

          return {
            ...conv,
            messages,
          };
        })
      );

      return conversationsWithMessages;
    } catch (error) {
      console.error('Failed to get conversations:', error);
      return [];
    }
  }

  static async deleteConversation(id: string): Promise<void> {
    try {
      await db.transaction('rw', [db.conversations, db.messages], async () => {
        await db.conversations.delete(id);
        await db.messages.where('conversation_id').equals(id).delete();
      });
    } catch (error) {
      console.error('Failed to delete conversation:', error);
      throw error;
    }
  }

  static async clearAllConversations(): Promise<void> {
    try {
      await db.transaction('rw', [db.conversations, db.messages], async () => {
        await db.conversations.clear();
        await db.messages.clear();
      });
    } catch (error) {
      console.error('Failed to clear conversations:', error);
      throw error;
    }
  }

  // Message storage
  static async saveMessage(message: Message, conversationId: string): Promise<void> {
    try {
      await db.messages.put({
        ...message,
        conversation_id: conversationId,
      });
    } catch (error) {
      console.error('Failed to save message:', error);
      throw error;
    }
  }

  static async updateMessage(messageId: string, updates: Partial<Message>): Promise<void> {
    try {
      await db.messages.update(messageId, updates);
    } catch (error) {
      console.error('Failed to update message:', error);
      throw error;
    }
  }

  static async deleteMessage(messageId: string): Promise<void> {
    try {
      await db.messages.delete(messageId);
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw error;
    }
  }

  // Preferences storage
  static async savePreferences(preferences: UserPreferences): Promise<void> {
    try {
      await db.preferences.put({ id: 'user-preferences', data: preferences });
    } catch (error) {
      console.error('Failed to save preferences:', error);
      throw error;
    }
  }

  static async getPreferences(): Promise<UserPreferences | null> {
    try {
      const result = await db.preferences.get('user-preferences');
      return result?.data || null;
    } catch (error) {
      console.error('Failed to get preferences:', error);
      return null;
    }
  }

  // Search functionality
  static async searchMessages(query: string): Promise<Message[]> {
    try {
      const lowercaseQuery = query.toLowerCase();
      const messages = await db.messages
        .filter(msg => msg.content.toLowerCase().includes(lowercaseQuery))
        .sortBy('timestamp');

      return messages.reverse(); // Most recent first
    } catch (error) {
      console.error('Failed to search messages:', error);
      return [];
    }
  }

  static async searchConversations(query: string): Promise<Conversation[]> {
    try {
      const lowercaseQuery = query.toLowerCase();

      // Search in conversation titles
      const titleMatches = await db.conversations
        .filter(conv => conv.title.toLowerCase().includes(lowercaseQuery))
        .toArray();

      // Search in message content
      const messageMatches = await db.messages
        .filter(msg => msg.content.toLowerCase().includes(lowercaseQuery))
        .toArray();

      // Get unique conversation IDs from message matches
      const conversationIds = new Set([
        ...titleMatches.map(conv => conv.id),
        ...messageMatches.map(msg => msg.conversation_id).filter(Boolean),
      ]);

      // Load full conversations with messages
      const conversations = await Promise.all(
        Array.from(conversationIds).filter(id => id).map(id => this.getConversation(id as string))
      );

      return conversations
        .filter((conv): conv is Conversation => conv !== null)
        .sort((a, b) => b.updated_at.getTime() - a.updated_at.getTime());
    } catch (error) {
      console.error('Failed to search conversations:', error);
      return [];
    }
  }

  // Database management
  static async exportData(): Promise<string> {
    try {
      const conversations = await this.getAllConversations();
      const preferences = await this.getPreferences();

      const exportData = {
        conversations,
        preferences,
        exportedAt: new Date().toISOString(),
        version: '1.0',
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Failed to export data:', error);
      throw error;
    }
  }

  static async importData(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData);

      if (!data.conversations || !Array.isArray(data.conversations)) {
        throw new Error('Invalid import data format');
      }

      await db.transaction('rw', [db.conversations, db.messages, db.preferences], async () => {
        // Clear existing data
        await db.conversations.clear();
        await db.messages.clear();

        // Import conversations and messages
        for (const conversation of data.conversations) {
          await this.saveConversation(conversation);
        }

        // Import preferences if available
        if (data.preferences) {
          await this.savePreferences(data.preferences);
        }
      });
    } catch (error) {
      console.error('Failed to import data:', error);
      throw error;
    }
  }

  static async getDatabaseSize(): Promise<{ conversations: number; messages: number }> {
    try {
      const conversationCount = await db.conversations.count();
      const messageCount = await db.messages.count();

      return {
        conversations: conversationCount,
        messages: messageCount,
      };
    } catch (error) {
      console.error('Failed to get database size:', error);
      return { conversations: 0, messages: 0 };
    }
  }
}
