import { ApiResponse, ChatRequest, ChatResponse, StreamChunk, User } from '../types';

// Base API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
const API_TIMEOUT = 30000; // 30 seconds

class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Generic API client
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(baseURL: string = API_BASE_URL, timeout: number = API_TIMEOUT) {
    this.baseURL = baseURL;
    this.timeout = timeout;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new ApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status
        );
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout', 408, 'TIMEOUT');
      }

      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0,
        'NETWORK_ERROR'
      );
    }
  }

  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', headers });
  }

  async post<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      headers,
    });
  }

  async put<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      headers,
    });
  }

  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE', headers });
  }

  // Server-Sent Events for streaming
  createEventSource(endpoint: string, options?: EventSourceInit): EventSource {
    const url = `${this.baseURL}${endpoint}`;
    return new EventSource(url, options);
  }
}

// Create API client instance
export const apiClient = new ApiClient();

// Chat API service
export class ChatService {
  static async sendMessage(request: ChatRequest): Promise<ApiResponse<ChatResponse>> {
    try {
      // Mock implementation for development
      return this.mockSendMessage(request);
    } catch (error) {
      console.error('Chat service error:', error);
      throw error;
    }
  }

  static createStreamingConnection(
    request: ChatRequest,
    onChunk: (chunk: StreamChunk) => void,
    onError: (error: Error) => void,
    onComplete: () => void
  ): () => void {
    try {
      // Mock streaming implementation
      return this.mockStreamingConnection(request, onChunk, onError, onComplete);
    } catch (error) {
      console.error('Streaming connection error:', error);
      onError(error as Error);
      return () => {};
    }
  }

  // Mock implementation for development
  private static async mockSendMessage(request: ChatRequest): Promise<ApiResponse<ChatResponse>> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Mock response generation
    const mockResponses = [
      "I understand your question. Let me help you with that.",
      "That's an interesting point. Here's what I think about it...",
      "Based on the information you've provided, I can suggest the following approach:",
      "Let me break this down for you step by step:",
      "Here's a comprehensive answer to your question:",
    ];

    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];

    return {
      success: true,
      data: {
        message: {
          id: `msg-${Date.now()}`,
          role: 'assistant',
          content: randomResponse + " " + request.message.split(' ').reverse().join(' '),
          timestamp: new Date(),
          status: 'delivered',
        },
        conversation_id: request.conversation_id || `conv-${Date.now()}`,
      },
    };
  }

  private static mockStreamingConnection(
    request: ChatRequest,
    onChunk: (chunk: StreamChunk) => void,
    onError: (error: Error) => void,
    onComplete: () => void
  ): () => void {
    const conversationId = request.conversation_id || `conv-${Date.now()}`;
    const messageId = `msg-${Date.now()}`;

    // Mock streaming response
    const fullResponse = `I understand your question about "${request.message}". Let me provide you with a detailed response that demonstrates the streaming capability of this chat interface. This message is being streamed token by token to simulate real-time AI response generation.`;

    const words = fullResponse.split(' ');
    let currentIndex = 0;
    let cancelled = false;

    const streamInterval = setInterval(() => {
      if (cancelled || currentIndex >= words.length) {
        clearInterval(streamInterval);
        if (!cancelled) {
          onChunk({
            id: messageId,
            content: '',
            done: true,
            conversation_id: conversationId,
          });
          onComplete();
        }
        return;
      }

      const chunk = words.slice(0, currentIndex + 1).join(' ');
      onChunk({
        id: messageId,
        content: chunk,
        done: false,
        conversation_id: conversationId,
      });

      currentIndex++;
    }, 100 + Math.random() * 200); // Variable delay to simulate real streaming

    // Return cleanup function
    return () => {
      cancelled = true;
      clearInterval(streamInterval);
    };
  }
}

// Auth API service
export class AuthService {
  static async login(email: string, password: string): Promise<ApiResponse<User>> {
    // Mock implementation
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      success: true,
      data: {
        id: 'user-1',
        email,
        name: 'Demo User',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
        preferences: {
          theme: 'system',
          model: 'gpt-4',
          font_size: 'medium',
          send_on_enter: true,
          show_timestamps: false,
          auto_scroll: true,
          sound_enabled: false,
          compact_mode: false,
        },
        subscription: {
          plan: 'pro',
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        },
      },
    };
  }

  static async logout(): Promise<ApiResponse<void>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  }

  static async getCurrentUser(): Promise<ApiResponse<User>> {
    // Mock implementation - check if user is stored locally
    const stored = localStorage.getItem('user-store');
    if (stored) {
      const data = JSON.parse(stored);
      if (data.state?.user) {
        return { success: true, data: data.state.user };
      }
    }

    throw new ApiError('Not authenticated', 401, 'UNAUTHORIZED');
  }
}

export { ApiError };
