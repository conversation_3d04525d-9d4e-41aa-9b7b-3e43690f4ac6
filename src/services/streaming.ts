import { StreamChunk, ChatRequest } from '../types';

export interface StreamingConnection {
  send: (request: ChatRequest) => void;
  stop: () => void;
  isConnected: boolean;
}

export interface StreamingOptions {
  onChunk: (chunk: StreamChunk) => void;
  onError: (error: Error) => void;
  onComplete: () => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export class StreamingService {
  private eventSource: EventSource | null = null;
  private isConnected = false;
  private options: StreamingOptions | null = null;

  constructor() {
    this.handleMessage = this.handleMessage.bind(this);
    this.handleError = this.handleError.bind(this);
    this.handleOpen = this.handleOpen.bind(this);
  }

  createConnection(options: StreamingOptions): StreamingConnection {
    this.options = options;

    return {
      send: this.send.bind(this),
      stop: this.stop.bind(this),
      get isConnected() {
        return this.isConnected;
      },
    };
  }

  private send(request: ChatRequest) {
    if (this.eventSource) {
      this.stop();
    }

    try {
      // In a real implementation, this would connect to your streaming endpoint
      // For demo purposes, we'll simulate streaming with a mock implementation
      this.simulateStreaming(request);
    } catch (error) {
      this.options?.onError(error as Error);
    }
  }

  private stop() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
    this.options?.onDisconnect?.();
  }

  private handleOpen() {
    this.isConnected = true;
    this.options?.onConnect?.();
  }

  private handleMessage(event: MessageEvent) {
    try {
      const data = JSON.parse(event.data);
      
      if (data.type === 'chunk') {
        this.options?.onChunk(data.chunk);
      } else if (data.type === 'complete') {
        this.options?.onComplete();
        this.stop();
      } else if (data.type === 'error') {
        this.options?.onError(new Error(data.error));
        this.stop();
      }
    } catch (error) {
      this.options?.onError(new Error('Failed to parse streaming data'));
    }
  }

  private handleError(event: Event) {
    this.options?.onError(new Error('Streaming connection error'));
    this.stop();
  }

  // Mock streaming implementation for demo
  private simulateStreaming(request: ChatRequest) {
    const conversationId = request.conversation_id || `conv-${Date.now()}`;
    const messageId = `msg-${Date.now()}`;
    
    // Generate a realistic response based on the user's message
    const responses = [
      `I understand you're asking about "${request.message}". Let me provide you with a comprehensive response that demonstrates the streaming capability of this chat interface.`,
      
      `That's an interesting question about "${request.message}". Here's what I think about it:

First, let me break this down into several key points that will help you understand the topic better.`,

      `Regarding "${request.message}", I can help you with that. This is a great example of how streaming responses work in real-time chat applications.

The streaming feature allows for:
- Real-time response generation
- Better user experience with immediate feedback
- Reduced perceived latency
- Progressive content delivery`,

      `Thank you for asking about "${request.message}". This is a complex topic that deserves a detailed explanation.

Let me walk you through this step by step:

1. **Understanding the basics**: The fundamental concepts you need to know
2. **Practical applications**: How this applies in real-world scenarios  
3. **Best practices**: Recommended approaches and methodologies
4. **Common pitfalls**: What to avoid and how to troubleshoot issues

Each of these points builds upon the previous one to give you a complete understanding of the subject matter.`,
    ];

    const selectedResponse = responses[Math.floor(Math.random() * responses.length)];
    const words = selectedResponse.split(' ');
    let currentIndex = 0;
    let currentContent = '';

    this.isConnected = true;
    this.options?.onConnect?.();

    const streamInterval = setInterval(() => {
      if (!this.isConnected || currentIndex >= words.length) {
        clearInterval(streamInterval);
        
        if (this.isConnected) {
          // Send final chunk
          this.options?.onChunk({
            id: messageId,
            content: currentContent,
            done: true,
            conversation_id: conversationId,
          });
          
          this.options?.onComplete();
          this.stop();
        }
        return;
      }

      // Add next word(s) - sometimes add multiple words for more natural streaming
      const wordsToAdd = Math.random() > 0.7 ? 2 : 1;
      const endIndex = Math.min(currentIndex + wordsToAdd, words.length);
      
      for (let i = currentIndex; i < endIndex; i++) {
        currentContent += (currentContent ? ' ' : '') + words[i];
      }
      
      currentIndex = endIndex;

      this.options?.onChunk({
        id: messageId,
        content: currentContent,
        done: false,
        conversation_id: conversationId,
      });

    }, 50 + Math.random() * 150); // Variable delay for realistic streaming

    // Store interval reference for cleanup
    this.eventSource = {
      close: () => {
        clearInterval(streamInterval);
        this.isConnected = false;
      }
    } as EventSource;
  }

  // Real SSE implementation (for when you have a backend)
  private createRealSSEConnection(endpoint: string, request: ChatRequest) {
    const url = new URL(endpoint);
    url.searchParams.set('message', request.message);
    if (request.conversation_id) {
      url.searchParams.set('conversation_id', request.conversation_id);
    }
    if (request.model) {
      url.searchParams.set('model', request.model);
    }

    this.eventSource = new EventSource(url.toString());
    
    this.eventSource.addEventListener('open', this.handleOpen);
    this.eventSource.addEventListener('message', this.handleMessage);
    this.eventSource.addEventListener('error', this.handleError);
    
    // Handle custom event types
    this.eventSource.addEventListener('chunk', (event) => {
      try {
        const chunk = JSON.parse(event.data);
        this.options?.onChunk(chunk);
      } catch (error) {
        this.options?.onError(new Error('Failed to parse chunk data'));
      }
    });

    this.eventSource.addEventListener('complete', () => {
      this.options?.onComplete();
      this.stop();
    });
  }

  // WebSocket implementation (alternative to SSE)
  private createWebSocketConnection(endpoint: string, request: ChatRequest) {
    const ws = new WebSocket(endpoint);
    
    ws.onopen = () => {
      this.isConnected = true;
      this.options?.onConnect?.();
      
      // Send the chat request
      ws.send(JSON.stringify(request));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'chunk') {
          this.options?.onChunk(data.chunk);
        } else if (data.type === 'complete') {
          this.options?.onComplete();
          ws.close();
        } else if (data.type === 'error') {
          this.options?.onError(new Error(data.error));
          ws.close();
        }
      } catch (error) {
        this.options?.onError(new Error('Failed to parse WebSocket data'));
      }
    };

    ws.onerror = () => {
      this.options?.onError(new Error('WebSocket connection error'));
    };

    ws.onclose = () => {
      this.isConnected = false;
      this.options?.onDisconnect?.();
    };

    // Store WebSocket reference for cleanup
    this.eventSource = {
      close: () => {
        ws.close();
        this.isConnected = false;
      }
    } as EventSource;
  }
}

// Singleton instance
export const streamingService = new StreamingService();
