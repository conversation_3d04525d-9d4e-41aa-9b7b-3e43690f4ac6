import React, { useMemo, useRef, useEffect, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Message } from '../../types';
import MessageBubble from '../chat/MessageBubble';

interface VirtualizedMessageListProps {
  messages: Message[];
  height: number;
  itemHeight?: number;
  onScrollToBottom?: () => void;
}

interface MessageItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    messages: Message[];
    isStreaming: (messageId: string) => boolean;
  };
}

const MessageItem: React.FC<MessageItemProps> = ({ index, style, data }) => {
  const { messages, isStreaming } = data;
  const message = messages[index];

  if (!message) return null;

  return (
    <div style={style}>
      <div className="px-4 py-2">
        <MessageBubble
          message={message}
          isStreaming={isStreaming(message.id)}
        />
      </div>
    </div>
  );
};

const VirtualizedMessageList: React.FC<VirtualizedMessageListProps> = ({
  messages,
  height,
  itemHeight = 120,
  onScrollToBottom,
}) => {
  const listRef = useRef<List>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);

  // Memoize item data to prevent unnecessary re-renders
  const itemData = useMemo(() => ({
    messages,
    isStreaming: (messageId: string) => {
      // Check if message is currently streaming
      const message = messages.find(m => m.id === messageId);
      return message?.status === 'sending' || false;
    },
  }), [messages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (isAtBottom && listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
    }
  }, [messages.length, isAtBottom]);

  // Handle scroll events to detect if user is at bottom
  const handleScroll = ({ scrollOffset, scrollUpdateWasRequested }: any) => {
    if (!scrollUpdateWasRequested) {
      // For virtual scrolling, we'll use a simpler approach
      const totalHeight = messages.length * itemHeight;
      const visibleHeight = height;
      const isNearBottom = scrollOffset + visibleHeight >= totalHeight - 50;
      setIsAtBottom(isNearBottom);
    }
  };

  // Scroll to bottom manually
  const scrollToBottom = () => {
    if (listRef.current && messages.length > 0) {
      listRef.current.scrollToItem(messages.length - 1, 'end');
      setIsAtBottom(true);
      onScrollToBottom?.();
    }
  };

  if (messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
        No messages yet. Start a conversation!
      </div>
    );
  }

  return (
    <div className="relative h-full">
      <List
        ref={listRef}
        height={height}
        width="100%"
        itemCount={messages.length}
        itemSize={itemHeight}
        itemData={itemData}
        onScroll={handleScroll}
        overscanCount={5} // Render 5 extra items for smooth scrolling
      >
        {MessageItem}
      </List>

      {/* Scroll to bottom button */}
      {!isAtBottom && (
        <button
          onClick={scrollToBottom}
          className="absolute bottom-4 right-4 p-3 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-colors z-10"
          title="Scroll to bottom"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default VirtualizedMessageList;
