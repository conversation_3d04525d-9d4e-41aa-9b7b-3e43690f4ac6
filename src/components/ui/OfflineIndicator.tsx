import React from 'react';
import { Wifi, WifiOff, Cloud, CloudOff, Loader2, X } from 'lucide-react';
import { useOfflineIndicator, useOfflineSync } from '../../hooks/useOfflineSync';
import { cn, formatDate } from '../../utils';

const OfflineIndicator: React.FC = () => {
  const { isOnline, showOfflineMessage, dismissOfflineMessage } = useOfflineIndicator();
  const { isSyncing, lastSyncTime, syncStatus, manualSync } = useOfflineSync();

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="w-4 h-4" />;
    } else if (isSyncing) {
      return <Loader2 className="w-4 h-4 animate-spin" />;
    } else if (syncStatus === 'synced') {
      return <Cloud className="w-4 h-4" />;
    } else {
      return <CloudOff className="w-4 h-4" />;
    }
  };

  const getStatusText = () => {
    if (!isOnline) {
      return 'Offline';
    } else if (isSyncing) {
      return 'Syncing...';
    } else if (syncStatus === 'synced') {
      return 'Synced';
    } else if (syncStatus === 'stale') {
      return 'Sync needed';
    } else {
      return 'Not synced';
    }
  };

  const getStatusColor = () => {
    if (!isOnline) {
      return 'text-red-600 dark:text-red-400';
    } else if (isSyncing) {
      return 'text-blue-600 dark:text-blue-400';
    } else if (syncStatus === 'synced') {
      return 'text-green-600 dark:text-green-400';
    } else {
      return 'text-yellow-600 dark:text-yellow-400';
    }
  };

  return (
    <>
      {/* Status indicator in header/sidebar */}
      <div className="flex items-center space-x-2">
        <button
          onClick={isOnline ? manualSync : undefined}
          disabled={isSyncing || !isOnline}
          className={cn(
            'flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors',
            getStatusColor(),
            isOnline && !isSyncing && 'hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer',
            (!isOnline || isSyncing) && 'cursor-not-allowed'
          )}
          title={
            lastSyncTime 
              ? `Last synced: ${formatDate(lastSyncTime)}`
              : 'Click to sync'
          }
        >
          {getStatusIcon()}
          <span>{getStatusText()}</span>
        </button>
      </div>

      {/* Offline notification banner */}
      {showOfflineMessage && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-yellow-500 text-white px-4 py-2 text-sm">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center space-x-2">
              <WifiOff className="w-4 h-4" />
              <span>
                You're offline. Your messages will be saved locally and synced when you're back online.
              </span>
            </div>
            <button
              onClick={dismissOfflineMessage}
              className="p-1 hover:bg-yellow-600 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Connection status toast */}
      {isOnline && syncStatus === 'synced' && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-fade-in">
          <Cloud className="w-4 h-4" />
          <span className="text-sm">Back online and synced</span>
        </div>
      )}
    </>
  );
};

export default OfflineIndicator;
