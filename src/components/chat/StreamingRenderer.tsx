import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';

interface StreamingRendererProps {
  content: string;
  speed?: number; // Characters per update
  interval?: number; // Milliseconds between updates
}

const StreamingRenderer: React.FC<StreamingRendererProps> = ({
  content,
  speed = 2,
  interval = 50,
}) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    // Reset when content changes
    setDisplayedContent('');
    setCurrentIndex(0);
    setIsComplete(false);
  }, [content]);

  useEffect(() => {
    if (currentIndex >= content.length) {
      setIsComplete(true);
      return;
    }

    const timer = setTimeout(() => {
      const nextIndex = Math.min(currentIndex + speed, content.length);
      setDisplayedContent(content.slice(0, nextIndex));
      setCurrentIndex(nextIndex);
    }, interval);

    return () => clearTimeout(timer);
  }, [content, currentIndex, speed, interval]);

  return (
    <div className="relative">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          code: ({ node, className, children, ...props }: any) => {
            const inline = !className;
            const match = /language-(\w+)/.exec(className || '');
            return !inline && match ? (
              <pre className="bg-gray-100 dark:bg-gray-800 rounded-md p-4 overflow-x-auto">
                <code className={className} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
        }}
      >
        {displayedContent}
      </ReactMarkdown>

      {/* Typing cursor */}
      {!isComplete && (
        <span className="inline-block w-2 h-4 bg-primary-600 dark:bg-primary-400 ml-1 animate-pulse" />
      )}
    </div>
  );
};

export default StreamingRenderer;
