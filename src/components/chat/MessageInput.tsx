import React, { useState, useRef, useEffect } from 'react';
import { 
  Send, 
  Paperclip, 
  X, 
  Image as ImageIcon,
  StopCircle
} from 'lucide-react';
import { useChat } from '../../hooks/useChat';
import { useTextInputShortcuts } from '../../hooks/useKeyboardShortcuts';
import { useUserStore } from '../../stores/userStore';
import { useUIStore } from '../../stores/uiStore';
import { cn, formatFileSize, isImageFile } from '../../utils';

interface MessageInputProps {
  onScrollToBottom?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({ onScrollToBottom }) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { sendMessage, stopStreaming, isLoading, isStreaming } = useChat({
    onMessageSent: () => {
      setMessage('');
      setAttachments([]);
      onScrollToBottom?.();
    },
    onMessageReceived: () => {
      onScrollToBottom?.();
    },
  });
  
  const { user } = useUserStore();
  const { error } = useUIStore();
  
  const sendOnEnter = user?.preferences?.send_on_enter ?? true;

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }, [message]);

  const handleSend = async () => {
    if (!message.trim() && attachments.length === 0) return;
    if (isLoading || isStreaming) return;

    await sendMessage(message, attachments);
  };

  const handleStop = () => {
    stopStreaming();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: new line
        return;
      } else if (sendOnEnter) {
        // Enter: send message
        e.preventDefault();
        handleSend();
      }
      // If sendOnEnter is false, Enter creates new line (default behavior)
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    setAttachments(prev => [...prev, ...files]);
  };

  const canSend = (message.trim() || attachments.length > 0) && !isLoading;

  return (
    <div className="p-4">
      {/* Error display */}
      {error && (
        <div className="mb-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      {/* Attachments */}
      {attachments.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {attachments.map((file, index) => (
            <div
              key={index}
              className="flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center gap-2">
                {isImageFile(file) ? (
                  <ImageIcon className="w-4 h-4 text-gray-500" />
                ) : (
                  <Paperclip className="w-4 h-4 text-gray-500" />
                )}
                <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-32">
                  {file.name}
                </span>
                <span className="text-xs text-gray-500">
                  {formatFileSize(file.size)}
                </span>
              </div>
              <button
                onClick={() => removeAttachment(index)}
                className="p-1 text-gray-500 hover:text-red-600 dark:hover:text-red-400 rounded transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Input area */}
      <div
        className={cn(
          'relative border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 transition-colors',
          isDragOver && 'border-primary-500 bg-primary-50 dark:bg-primary-900/20',
          'focus-within:border-primary-500 focus-within:ring-1 focus-within:ring-primary-500'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type your message... (Shift+Enter for new line)"
          className="w-full px-4 py-3 pr-20 bg-transparent border-none outline-none resize-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          rows={1}
          disabled={isLoading}
          data-testid="message-input"
        />

        {/* Action buttons */}
        <div className="absolute right-2 bottom-2 flex items-center gap-1">
          {/* File upload button */}
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Attach file"
          >
            <Paperclip className="w-4 h-4" />
          </button>

          {/* Send/Stop button */}
          {isStreaming ? (
            <button
              onClick={handleStop}
              className="p-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
              title="Stop generating"
            >
              <StopCircle className="w-4 h-4" />
            </button>
          ) : (
            <button
              onClick={handleSend}
              disabled={!canSend}
              className={cn(
                'p-2 rounded-lg transition-colors',
                canSend
                  ? 'text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:bg-primary-50 dark:hover:bg-primary-900/20'
                  : 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
              )}
              title="Send message"
            >
              <Send className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf,.doc,.docx,.txt"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Character count and shortcuts hint */}
      <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
        <div>
          {message.length > 0 && (
            <span>{message.length} characters</span>
          )}
        </div>
        <div className="flex items-center gap-4">
          <span>
            {sendOnEnter ? 'Enter to send, Shift+Enter for new line' : 'Enter for new line'}
          </span>
          <span>Ctrl+/ for shortcuts</span>
        </div>
      </div>

      {/* Drag overlay */}
      {isDragOver && (
        <div className="absolute inset-0 bg-primary-500/10 border-2 border-dashed border-primary-500 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <Paperclip className="w-8 h-8 text-primary-600 mx-auto mb-2" />
            <p className="text-primary-600 font-medium">Drop files here to attach</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageInput;
