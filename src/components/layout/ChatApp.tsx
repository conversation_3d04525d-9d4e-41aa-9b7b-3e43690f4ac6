import React, { useEffect } from 'react';
import { useUIStore } from '../../stores/uiStore';
import { useUserStore } from '../../stores/userStore';
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts';
import Sidebar from './Sidebar';
import ChatPanel from './ChatPanel';
import SettingsModal from '../modals/SettingsModal';
import ErrorBoundary from '../ui/ErrorBoundary';
import LoadingSpinner from '../ui/LoadingSpinner';
import { cn } from '../../utils';

const ChatApp: React.FC = () => {
  const { sidebarOpen, isMobile, loading } = useUIStore();
  const { isAuthenticated, isLoading } = useUserStore();
  
  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  // Handle initial loading and authentication
  useEffect(() => {
    // Auto-login with mock user for demo
    if (!isAuthenticated && !isLoading) {
      const mockUser = {
        id: 'demo-user',
        email: '<EMAIL>',
        name: 'Demo User',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
        preferences: {
          theme: 'system' as const,
          model: 'gpt-4',
          font_size: 'medium' as const,
          send_on_enter: true,
          show_timestamps: false,
          auto_scroll: true,
          sound_enabled: false,
          compact_mode: false,
        },
        subscription: {
          plan: 'pro' as const,
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        },
      };
      useUserStore.getState().login(mockUser);
    }
  }, [isAuthenticated, isLoading]);

  if (isLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
        {/* Sidebar */}
        <div
          className={cn(
            'flex-shrink-0 transition-all duration-300 ease-in-out',
            sidebarOpen ? 'w-80' : 'w-0',
            isMobile && sidebarOpen && 'absolute inset-y-0 left-0 z-50 w-80'
          )}
        >
          <Sidebar />
        </div>

        {/* Mobile overlay */}
        {isMobile && sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={() => useUIStore.getState().setSidebarOpen(false)}
          />
        )}

        {/* Main content area */}
        <div className="flex-1 flex flex-col min-w-0">
          <ChatPanel />
        </div>

        {/* Settings Modal */}
        <SettingsModal />
      </div>
    </ErrorBoundary>
  );
};

export default ChatApp;
