import React, { useEffect, useRef } from 'react';
import { Menu } from 'lucide-react';
import { useConversationStore } from '../../stores/conversationStore';
import { useUIStore } from '../../stores/uiStore';
import MessageBubble from '../chat/MessageBubble';
import MessageInput from '../chat/MessageInput';
import { cn } from '../../utils';

const ChatPanel: React.FC = () => {
  const { getCurrentConversation } = useConversationStore();
  const { sidebarOpen, toggleSidebar, isMobile } = useUIStore();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatPanelRef = useRef<HTMLDivElement>(null);
  
  const currentConversation = getCurrentConversation();
  const messages = currentConversation?.messages || [];

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages.length]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-center space-x-3">
          {(!sidebarOpen || isMobile) && (
            <button
              onClick={toggleSidebar}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Open sidebar"
            >
              <Menu className="w-5 h-5" />
            </button>
          )}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {currentConversation?.title || 'New Chat'}
            </h2>
            {currentConversation && messages.length > 0 && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {messages.length} message{messages.length !== 1 ? 's' : ''}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div
        ref={chatPanelRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        data-testid="chat-panel"
      >
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 mx-auto mb-4 bg-primary-100 dark:bg-primary-900/20 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-primary-600 dark:text-primary-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Start a conversation
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Ask me anything! I'm here to help with questions, creative tasks, analysis, and more.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium text-gray-900 dark:text-white mb-1">
                    💡 Get ideas
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Brainstorm creative solutions
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium text-gray-900 dark:text-white mb-1">
                    📝 Write content
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Create articles, emails, code
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium text-gray-900 dark:text-white mb-1">
                    🔍 Analyze data
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Break down complex information
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="font-medium text-gray-900 dark:text-white mb-1">
                    🎓 Learn topics
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">
                    Explain concepts clearly
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                isStreaming={
                  message.role === 'assistant' && 
                  index === messages.length - 1 && 
                  message.status === 'sending'
                }
              />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <MessageInput onScrollToBottom={scrollToBottom} />
      </div>
    </div>
  );
};

export default ChatPanel;
