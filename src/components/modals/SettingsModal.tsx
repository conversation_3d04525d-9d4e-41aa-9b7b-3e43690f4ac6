import React, { useState } from 'react';
import { 
  X, 
  Monitor, 
  Sun, 
  Moon, 
  Download, 
  Upload, 
  Trash2,
  Save
} from 'lucide-react';
import { useUIStore } from '../../stores/uiStore';
import { useUserStore } from '../../stores/userStore';
import { useConversationStore } from '../../stores/conversationStore';
import { StorageService } from '../../services/storage';
import { downloadFile, cn } from '../../utils';

const SettingsModal: React.FC = () => {
  const { settingsOpen, setSettingsOpen, theme, setTheme } = useUIStore();
  const { user, updatePreferences } = useUserStore();
  const { clearAllConversations } = useConversationStore();
  
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);

  if (!settingsOpen || !user) return null;

  const preferences = user.preferences;

  const handleClose = () => {
    setSettingsOpen(false);
  };

  const handlePreferenceChange = <K extends keyof typeof preferences>(
    key: K,
    value: typeof preferences[K]
  ) => {
    updatePreferences({ [key]: value });
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    handlePreferenceChange('theme', newTheme);
  };

  const handleExportData = async () => {
    try {
      setIsExporting(true);
      const data = await StorageService.exportData();
      const filename = `chatgpt-clone-export-${new Date().toISOString().split('T')[0]}.json`;
      downloadFile(data, filename, 'application/json');
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export data. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsImporting(true);
      setImportError(null);
      
      const text = await file.text();
      await StorageService.importData(text);
      
      // Refresh the page to reload imported data
      window.location.reload();
    } catch (error) {
      console.error('Import failed:', error);
      setImportError(error instanceof Error ? error.message : 'Failed to import data');
    } finally {
      setIsImporting(false);
      // Reset file input
      event.target.value = '';
    }
  };

  const handleClearAllData = () => {
    if (confirm('Are you sure you want to delete all conversations? This action cannot be undone.')) {
      clearAllConversations();
      StorageService.clearAllConversations();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Settings
          </h2>
          <button
            onClick={handleClose}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Appearance */}
          <section>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Appearance
            </h3>
            
            <div className="space-y-4">
              {/* Theme */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Theme
                </label>
                <div className="flex gap-2">
                  {[
                    { value: 'light', label: 'Light', icon: Sun },
                    { value: 'dark', label: 'Dark', icon: Moon },
                    { value: 'system', label: 'System', icon: Monitor },
                  ].map(({ value, label, icon: Icon }) => (
                    <button
                      key={value}
                      onClick={() => handleThemeChange(value as any)}
                      className={cn(
                        'flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors',
                        theme === value
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                          : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                      )}
                    >
                      <Icon className="w-4 h-4" />
                      {label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Font Size */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Font Size
                </label>
                <select
                  value={preferences.font_size}
                  onChange={(e) => handlePreferenceChange('font_size', e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>
            </div>
          </section>

          {/* Chat Behavior */}
          <section>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Chat Behavior
            </h3>
            
            <div className="space-y-4">
              {/* Send on Enter */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Send on Enter
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Press Enter to send messages (Shift+Enter for new line)
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.send_on_enter}
                  onChange={(e) => handlePreferenceChange('send_on_enter', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>

              {/* Show Timestamps */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Show Timestamps
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Display message timestamps
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.show_timestamps}
                  onChange={(e) => handlePreferenceChange('show_timestamps', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>

              {/* Auto Scroll */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Auto Scroll
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Automatically scroll to new messages
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.auto_scroll}
                  onChange={(e) => handlePreferenceChange('auto_scroll', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>

              {/* Compact Mode */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Compact Mode
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Reduce spacing between messages
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.compact_mode}
                  onChange={(e) => handlePreferenceChange('compact_mode', e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>
            </div>
          </section>

          {/* Data Management */}
          <section>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Data Management
            </h3>
            
            <div className="space-y-4">
              {/* Export Data */}
              <div>
                <button
                  onClick={handleExportData}
                  disabled={isExporting}
                  className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg transition-colors"
                >
                  <Download className="w-4 h-4" />
                  {isExporting ? 'Exporting...' : 'Export Data'}
                </button>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Download all your conversations and settings
                </p>
              </div>

              {/* Import Data */}
              <div>
                <label className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg cursor-pointer transition-colors">
                  <Upload className="w-4 h-4" />
                  {isImporting ? 'Importing...' : 'Import Data'}
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportData}
                    disabled={isImporting}
                    className="hidden"
                  />
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Upload a previously exported data file
                </p>
                {importError && (
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    {importError}
                  </p>
                )}
              </div>

              {/* Clear All Data */}
              <div>
                <button
                  onClick={handleClearAllData}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear All Data
                </button>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Delete all conversations and reset settings
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
