import { useEffect, useCallback } from 'react';
import { useUIStore } from '../stores/uiStore';
import { useConversationStore } from '../stores/conversationStore';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  preventDefault?: boolean;
}

export function useKeyboardShortcuts() {
  const { toggleSidebar, toggleSettings, setShortcutsVisible } = useUIStore();
  const { createConversation, deleteConversation, currentConversationId } = useConversationStore();

  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'n',
      ctrlKey: true,
      action: () => createConversation(),
      description: 'New conversation',
      preventDefault: true,
    },
    {
      key: 'b',
      ctrlKey: true,
      action: () => toggleSidebar(),
      description: 'Toggle sidebar',
      preventDefault: true,
    },
    {
      key: ',',
      ctrlKey: true,
      action: () => toggleSettings(),
      description: 'Open settings',
      preventDefault: true,
    },
    {
      key: '/',
      ctrlKey: true,
      action: () => setShortcutsVisible(true),
      description: 'Show keyboard shortcuts',
      preventDefault: true,
    },
    {
      key: 'Delete',
      ctrlKey: true,
      shiftKey: true,
      action: () => {
        if (currentConversationId && confirm('Delete this conversation?')) {
          deleteConversation(currentConversationId);
        }
      },
      description: 'Delete current conversation',
      preventDefault: true,
    },
    {
      key: 'Escape',
      action: () => {
        setShortcutsVisible(false);
        // Close any open modals
        const { settingsOpen } = useUIStore.getState();
        if (settingsOpen) {
          toggleSettings();
        }
      },
      description: 'Close modals/dialogs',
      preventDefault: false,
    },
  ];

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in input fields
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      // Allow Escape to work in input fields
      if (event.key !== 'Escape') {
        return;
      }
    }

    for (const shortcut of shortcuts) {
      const keyMatches = event.key === shortcut.key;
      const ctrlMatches = !!shortcut.ctrlKey === event.ctrlKey;
      const shiftMatches = !!shortcut.shiftKey === event.shiftKey;
      const altMatches = !!shortcut.altKey === event.altKey;
      const metaMatches = !!shortcut.metaKey === event.metaKey;

      if (keyMatches && ctrlMatches && shiftMatches && altMatches && metaMatches) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault();
        }
        shortcut.action();
        break;
      }
    }
  }, [shortcuts, toggleSidebar, toggleSettings, setShortcutsVisible, createConversation, deleteConversation, currentConversationId]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { shortcuts };
}

// Hook for managing text input shortcuts
export function useTextInputShortcuts(
  onSend: () => void,
  onNewLine: () => void,
  sendOnEnter: boolean = true
) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift+Enter always creates new line
        onNewLine();
      } else if (sendOnEnter) {
        // Enter sends message (if enabled)
        event.preventDefault();
        onSend();
      } else {
        // Enter creates new line (if send on enter is disabled)
        onNewLine();
      }
    }
  }, [onSend, onNewLine, sendOnEnter]);

  return { handleKeyDown };
}

// Hook for managing focus and navigation
export function useFocusManagement() {
  const focusMessageInput = useCallback(() => {
    const input = document.querySelector('[data-testid="message-input"]') as HTMLTextAreaElement;
    if (input) {
      input.focus();
    }
  }, []);

  const focusSearchInput = useCallback(() => {
    const input = document.querySelector('[data-testid="search-input"]') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    const chatPanel = document.querySelector('[data-testid="chat-panel"]');
    if (chatPanel) {
      chatPanel.scrollTop = chatPanel.scrollHeight;
    }
  }, []);

  const scrollToTop = useCallback(() => {
    const chatPanel = document.querySelector('[data-testid="chat-panel"]');
    if (chatPanel) {
      chatPanel.scrollTop = 0;
    }
  }, []);

  return {
    focusMessageInput,
    focusSearchInput,
    scrollToBottom,
    scrollToTop,
  };
}
