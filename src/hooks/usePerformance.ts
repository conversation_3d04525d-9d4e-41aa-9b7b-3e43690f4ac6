import { useEffect, useCallback, useRef, useState } from 'react';
import { debounce, throttle } from '../utils';

// Hook for performance monitoring
export function usePerformance() {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    memoryUsage: 0,
    fps: 0,
  });

  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const renderStartTime = useRef(0);

  // Measure render performance
  const startRender = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  const endRender = useCallback(() => {
    const renderTime = performance.now() - renderStartTime.current;
    setMetrics(prev => ({ ...prev, renderTime }));
  }, []);

  // Monitor FPS
  useEffect(() => {
    let animationId: number;

    const measureFPS = () => {
      frameCount.current++;
      const currentTime = performance.now();

      if (currentTime - lastTime.current >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));
        setMetrics(prev => ({ ...prev, fps }));

        frameCount.current = 0;
        lastTime.current = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    measureFPS();

    return () => cancelAnimationFrame(animationId);
  }, []);

  // Monitor memory usage
  useEffect(() => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
        setMetrics(prev => ({ ...prev, memoryUsage }));
      }
    };

    const interval = setInterval(updateMemoryUsage, 5000);
    updateMemoryUsage(); // Initial measurement

    return () => clearInterval(interval);
  }, []);

  return {
    metrics,
    startRender,
    endRender,
  };
}

// Hook for optimizing expensive operations
export function useOptimization() {
  // Debounced search
  const createDebouncedSearch = useCallback((
    searchFn: (query: string) => void,
    delay: number = 300
  ) => {
    return debounce(searchFn, delay);
  }, []);

  // Throttled scroll handler
  const createThrottledScroll = useCallback((
    scrollFn: (event: Event) => void,
    limit: number = 100
  ) => {
    return throttle(scrollFn, limit);
  }, []);

  // Memoized expensive calculations
  const memoize = useCallback(<T extends (...args: any[]) => any>(fn: T): T => {
    const cache = new Map();

    return ((...args: Parameters<T>) => {
      const key = JSON.stringify(args);

      if (cache.has(key)) {
        return cache.get(key);
      }

      const result = fn(...args);
      cache.set(key, result);

      // Limit cache size
      if (cache.size > 100) {
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }

      return result;
    }) as T;
  }, []);

  // Lazy loading for images
  const createLazyLoader = useCallback(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;

            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          }
        });
      },
      { threshold: 0.1 }
    );

    return {
      observe: (element: HTMLImageElement) => observer.observe(element),
      disconnect: () => observer.disconnect(),
    };
  }, []);

  return {
    createDebouncedSearch,
    createThrottledScroll,
    memoize,
    createLazyLoader,
  };
}

// Hook for bundle splitting and code splitting
export function useCodeSplitting() {
  const [loadedModules, setLoadedModules] = useState<Set<string>>(new Set());
  const [loadingModules, setLoadingModules] = useState<Set<string>>(new Set());

  const loadModule = useCallback(async <T>(
    moduleLoader: () => Promise<T>,
    moduleName: string
  ): Promise<T> => {
    if (loadedModules.has(moduleName)) {
      return moduleLoader();
    }

    setLoadingModules(prev => new Set(prev).add(moduleName));

    try {
      const module = await moduleLoader();
      setLoadedModules(prev => new Set(prev).add(moduleName));
      return module;
    } finally {
      setLoadingModules(prev => {
        const newSet = new Set(prev);
        newSet.delete(moduleName);
        return newSet;
      });
    }
  }, [loadedModules]);

  const isModuleLoaded = useCallback((moduleName: string) => {
    return loadedModules.has(moduleName);
  }, [loadedModules]);

  const isModuleLoading = useCallback((moduleName: string) => {
    return loadingModules.has(moduleName);
  }, [loadingModules]);

  return {
    loadModule,
    isModuleLoaded,
    isModuleLoading,
    loadedModules: Array.from(loadedModules),
    loadingModules: Array.from(loadingModules),
  };
}

// Hook for managing component updates
export function useUpdateOptimization() {
  const renderCount = useRef(0);
  const lastProps = useRef<any>(null);
  const lastState = useRef<any>(null);

  // Track render count
  useEffect(() => {
    renderCount.current++;
  });

  // Shallow compare for props/state changes
  const hasPropsChanged = useCallback((props: any) => {
    if (!lastProps.current) {
      lastProps.current = props;
      return true;
    }

    const changed = Object.keys(props).some(
      key => props[key] !== lastProps.current[key]
    );

    if (changed) {
      lastProps.current = props;
    }

    return changed;
  }, []);

  const hasStateChanged = useCallback((state: any) => {
    if (!lastState.current) {
      lastState.current = state;
      return true;
    }

    const changed = Object.keys(state).some(
      key => state[key] !== lastState.current[key]
    );

    if (changed) {
      lastState.current = state;
    }

    return changed;
  }, []);

  // Create optimized event handlers
  const createStableHandler = useCallback(<T extends (...args: any[]) => any>(
    handler: T,
    deps: any[]
  ): T => {
    const handlerRef = useRef(handler);
    const depsRef = useRef(deps);

    // Update handler if dependencies changed
    if (!depsRef.current || deps.some((dep, i) => dep !== depsRef.current[i])) {
      handlerRef.current = handler;
      depsRef.current = deps;
    }

    return handlerRef.current;
  }, []);

  return {
    renderCount: renderCount.current,
    hasPropsChanged,
    hasStateChanged,
    createStableHandler,
  };
}

// Hook for managing expensive computations
export function useExpensiveComputation<T>(
  computeFn: () => T,
  deps: any[],
  options: {
    timeout?: number;
    fallback?: T;
  } = {}
) {
  const [result, setResult] = useState<T | undefined>(options.fallback);
  const [isComputing, setIsComputing] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const computationRef = useRef<number | undefined>(undefined);

  useEffect(() => {
    setIsComputing(true);
    setError(null);

    // Use setTimeout to avoid blocking the main thread
    computationRef.current = window.setTimeout(async () => {
      try {
        const computed = computeFn();
        setResult(computed);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Computation failed'));
      } finally {
        setIsComputing(false);
      }
    }, 0);

    // Cleanup timeout if dependencies change
    return () => {
      if (computationRef.current) {
        clearTimeout(computationRef.current);
      }
    };
  }, deps);

  // Handle timeout
  useEffect(() => {
    if (options.timeout && isComputing) {
      const timeoutId = setTimeout(() => {
        if (computationRef.current) {
          clearTimeout(computationRef.current);
        }
        setError(new Error('Computation timeout'));
        setIsComputing(false);
      }, options.timeout);

      return () => clearTimeout(timeoutId);
    }
  }, [isComputing, options.timeout]);

  return {
    result,
    isComputing,
    error,
  };
}
