import { useState, useEffect, useCallback } from 'react';
import { getFromStorage, setToStorage, removeFromStorage } from '../utils';

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((prev: T) => T)) => void, () => void] {
  // Get initial value from localStorage or use provided initial value
  const [storedValue, setStoredValue] = useState<T>(() => {
    return getFromStorage(key, initialValue);
  });

  // Update localStorage when state changes
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      setToStorage(key, valueToStore);
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue);
      removeFromStorage(key);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  // Listen for changes to this localStorage key from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.error(`Error parsing localStorage value for key "${key}":`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  return [storedValue, setValue, removeValue];
}

// Hook for managing user preferences in localStorage
export function useUserPreferences() {
  const [preferences, setPreferences, removePreferences] = useLocalStorage('user-preferences', {
    theme: 'system' as 'light' | 'dark' | 'system',
    fontSize: 'medium' as 'small' | 'medium' | 'large',
    sendOnEnter: true,
    showTimestamps: false,
    autoScroll: true,
    soundEnabled: false,
    compactMode: false,
  });

  const updatePreference = useCallback(<K extends keyof typeof preferences>(
    key: K,
    value: typeof preferences[K]
  ) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  }, [setPreferences]);

  return {
    preferences,
    updatePreference,
    resetPreferences: removePreferences,
  };
}

// Hook for managing recent searches
export function useRecentSearches(maxItems: number = 10) {
  const [searches, setSearches, clearSearches] = useLocalStorage<string[]>('recent-searches', []);

  const addSearch = useCallback((query: string) => {
    if (!query.trim()) return;

    setSearches(prev => {
      const filtered = prev.filter(item => item !== query);
      return [query, ...filtered].slice(0, maxItems);
    });
  }, [setSearches, maxItems]);

  const removeSearch = useCallback((query: string) => {
    setSearches(prev => prev.filter(item => item !== query));
  }, [setSearches]);

  return {
    searches,
    addSearch,
    removeSearch,
    clearSearches,
  };
}

// Hook for managing window/tab state
export function useWindowState() {
  const [windowState, setWindowState] = useLocalStorage('window-state', {
    sidebarWidth: 280,
    sidebarCollapsed: false,
    lastActiveTab: 'chat',
  });

  const updateWindowState = useCallback(<K extends keyof typeof windowState>(
    key: K,
    value: typeof windowState[K]
  ) => {
    setWindowState(prev => ({ ...prev, [key]: value }));
  }, [setWindowState]);

  return {
    windowState,
    updateWindowState,
  };
}

// Hook for managing draft messages
export function useDraftMessage(conversationId: string | null) {
  const key = conversationId ? `draft-${conversationId}` : 'draft-new';
  const [draft, setDraft, clearDraft] = useLocalStorage(key, '');

  // Auto-clear draft when conversation changes
  useEffect(() => {
    if (!conversationId) {
      clearDraft();
    }
  }, [conversationId, clearDraft]);

  return {
    draft,
    setDraft,
    clearDraft,
  };
}

// Hook for managing export/import history
export function useExportHistory() {
  const [history, setHistory] = useLocalStorage<Array<{
    timestamp: string;
    filename: string;
    size: number;
  }>>('export-history', []);

  const addExport = useCallback((filename: string, size: number) => {
    const entry = {
      timestamp: new Date().toISOString(),
      filename,
      size,
    };

    setHistory(prev => [entry, ...prev].slice(0, 20)); // Keep last 20 exports
  }, [setHistory]);

  return {
    history,
    addExport,
  };
}
