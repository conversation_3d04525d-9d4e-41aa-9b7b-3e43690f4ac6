import { useCallback, useRef, useState } from 'react';
import { useConversationStore } from '../stores/conversationStore';
import { useUIStore } from '../stores/uiStore';
import { ChatService } from '../services/api';
import { Message, StreamChunk } from '../types';

export interface UseChatOptions {
  onError?: (error: Error) => void;
  onMessageSent?: (message: Message) => void;
  onMessageReceived?: (message: Message) => void;
  autoScroll?: boolean;
}

export function useChat(options: UseChatOptions = {}) {
  const {
    addMessage,
    updateMessage,
    getCurrentConversation,
    createConversation,
    currentConversationId,
  } = useConversationStore();
  
  const { setStreaming, setError } = useUIStore();
  
  const [isLoading, setIsLoading] = useState(false);
  const streamingRef = useRef<(() => void) | null>(null);

  const sendMessage = useCallback(async (
    content: string,
    attachments?: File[]
  ) => {
    if (!content.trim()) return;

    try {
      setIsLoading(true);
      setError(null);

      // Get or create conversation
      let conversationId = currentConversationId;
      if (!conversationId) {
        conversationId = createConversation();
      }

      // Add user message
      const userMessageId = addMessage(conversationId, {
        role: 'user',
        content: content.trim(),
        status: 'sent',
        attachments,
      });

      const userMessage = getCurrentConversation()?.messages.find(m => m.id === userMessageId);
      if (userMessage && options.onMessageSent) {
        options.onMessageSent(userMessage);
      }

      // Start streaming response
      const assistantMessageId = addMessage(conversationId, {
        role: 'assistant',
        content: '',
        status: 'sending',
      });

      setStreaming(true, assistantMessageId);

      // Create streaming connection
      const cleanup = ChatService.createStreamingConnection(
        {
          message: content,
          conversation_id: conversationId,
          attachments,
          stream: true,
        },
        (chunk: StreamChunk) => {
          // Update message content with streamed text
          updateMessage(conversationId!, assistantMessageId, {
            content: chunk.content,
            status: chunk.done ? 'delivered' : 'sending',
          });

          if (chunk.done) {
            setStreaming(false);
            const finalMessage = getCurrentConversation()?.messages.find(m => m.id === assistantMessageId);
            if (finalMessage && options.onMessageReceived) {
              options.onMessageReceived(finalMessage);
            }
          }
        },
        (error: Error) => {
          console.error('Streaming error:', error);
          setStreaming(false);
          updateMessage(conversationId!, assistantMessageId, {
            status: 'error',
            content: 'Sorry, I encountered an error while processing your message.',
          });
          setError(error.message);
          if (options.onError) {
            options.onError(error);
          }
        },
        () => {
          setStreaming(false);
          streamingRef.current = null;
        }
      );

      streamingRef.current = cleanup;

    } catch (error) {
      console.error('Send message error:', error);
      setStreaming(false);
      setError(error instanceof Error ? error.message : 'Failed to send message');
      if (options.onError) {
        options.onError(error instanceof Error ? error : new Error('Failed to send message'));
      }
    } finally {
      setIsLoading(false);
    }
  }, [
    currentConversationId,
    createConversation,
    addMessage,
    updateMessage,
    getCurrentConversation,
    setStreaming,
    setError,
    options,
  ]);

  const stopStreaming = useCallback(() => {
    if (streamingRef.current) {
      streamingRef.current();
      streamingRef.current = null;
      setStreaming(false);
    }
  }, [setStreaming]);

  const retryMessage = useCallback(async (messageId: string) => {
    const conversation = getCurrentConversation();
    if (!conversation) return;

    const messageIndex = conversation.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    const message = conversation.messages[messageIndex];
    if (message.role !== 'user') return;

    // Find the assistant's response (next message)
    const assistantMessage = conversation.messages[messageIndex + 1];
    if (assistantMessage && assistantMessage.role === 'assistant') {
      // Remove the failed assistant message
      updateMessage(conversation.id, assistantMessage.id, {
        content: '',
        status: 'sending',
      });
    }

    // Resend the user message
    await sendMessage(message.content, message.attachments);
  }, [getCurrentConversation, sendMessage, updateMessage]);

  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    const conversation = getCurrentConversation();
    if (!conversation) return;

    const messageIndex = conversation.messages.findIndex(m => m.id === messageId);
    if (messageIndex === -1) return;

    const message = conversation.messages[messageIndex];
    if (message.role !== 'user') return;

    // Update the user message
    updateMessage(conversation.id, messageId, {
      content: newContent,
    });

    // Remove all subsequent messages (they're now invalid)
    const subsequentMessages = conversation.messages.slice(messageIndex + 1);
    subsequentMessages.forEach(msg => {
      updateMessage(conversation.id, msg.id, { status: 'error' });
    });

    // Resend with new content
    await sendMessage(newContent, message.attachments);
  }, [getCurrentConversation, updateMessage, sendMessage]);

  return {
    sendMessage,
    stopStreaming,
    retryMessage,
    editMessage,
    isLoading,
    isStreaming: useUIStore(state => state.isStreaming),
    streamingMessageId: useUIStore(state => state.streamingMessageId),
  };
}
