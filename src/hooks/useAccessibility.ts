import { useEffect, useCallback, useRef, useState } from 'react';
import { useUIStore } from '../stores/uiStore';

// Hook for managing focus and ARIA announcements
export function useAccessibility() {
  const announcementRef = useRef<HTMLDivElement>(null);

  // Announce messages to screen readers
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announcementRef.current) {
      announcementRef.current.setAttribute('aria-live', priority);
      announcementRef.current.textContent = message;

      // Clear after announcement
      setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  // Focus management for modals and dialogs
  const trapFocus = useCallback((element: HTMLElement) => {
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    element.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      element.removeEventListener('keydown', handleTabKey);
    };
  }, []);

  // Skip to main content
  const skipToMain = useCallback(() => {
    const mainContent = document.querySelector('[role="main"]') as HTMLElement;
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView();
    }
  }, []);

  // High contrast mode detection
  const [isHighContrast, setIsHighContrast] = useState(false);

  useEffect(() => {
    const checkHighContrast = () => {
      const testElement = document.createElement('div');
      testElement.style.border = '1px solid';
      testElement.style.borderColor = 'red green';
      document.body.appendChild(testElement);

      const computedStyle = window.getComputedStyle(testElement);
      const isHighContrast = computedStyle.borderTopColor === computedStyle.borderRightColor;

      document.body.removeChild(testElement);
      setIsHighContrast(isHighContrast);
    };

    checkHighContrast();

    // Listen for changes
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    mediaQuery.addEventListener('change', checkHighContrast);

    return () => mediaQuery.removeEventListener('change', checkHighContrast);
  }, []);

  // Reduced motion preference
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Screen reader detection
  const [isScreenReader, setIsScreenReader] = useState(false);

  useEffect(() => {
    // Simple screen reader detection
    const userAgent = navigator.userAgent.toLowerCase();
    const isScreenReaderUA = /jaws|nvda|voiceover|narrator|dragon|talkback/.test(userAgent);

    // Check for screen reader specific APIs
    const hasScreenReaderAPI = 'speechSynthesis' in window || 'webkitSpeechSynthesis' in window;

    setIsScreenReader(isScreenReaderUA || hasScreenReaderAPI);
  }, []);

  return {
    announce,
    trapFocus,
    skipToMain,
    isHighContrast,
    prefersReducedMotion,
    isScreenReader,
    announcementRef,
  };
}

// Hook for keyboard navigation
export function useKeyboardNavigation() {
  const { sidebarOpen, setSidebarOpen } = useUIStore();

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Skip if user is typing in an input
      const target = e.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }

      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          navigateMessages('up');
          break;
        case 'ArrowDown':
          e.preventDefault();
          navigateMessages('down');
          break;
        case 'Home':
          e.preventDefault();
          scrollToTop();
          break;
        case 'End':
          e.preventDefault();
          scrollToBottom();
          break;
        case 'Tab':
          if (e.altKey) {
            e.preventDefault();
            navigateConversations(e.shiftKey ? 'previous' : 'next');
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [sidebarOpen]);

  const navigateMessages = (direction: 'up' | 'down') => {
    const messages = document.querySelectorAll('[data-message-id]');
    const currentFocus = document.activeElement;

    let currentIndex = -1;
    messages.forEach((msg, index) => {
      if (msg.contains(currentFocus)) {
        currentIndex = index;
      }
    });

    let nextIndex;
    if (direction === 'up') {
      nextIndex = currentIndex > 0 ? currentIndex - 1 : messages.length - 1;
    } else {
      nextIndex = currentIndex < messages.length - 1 ? currentIndex + 1 : 0;
    }

    const nextMessage = messages[nextIndex] as HTMLElement;
    if (nextMessage) {
      nextMessage.focus();
      nextMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const navigateConversations = (direction: 'next' | 'previous') => {
    const conversations = document.querySelectorAll('[data-conversation-id]');
    const currentFocus = document.activeElement;

    let currentIndex = -1;
    conversations.forEach((conv, index) => {
      if (conv.contains(currentFocus)) {
        currentIndex = index;
      }
    });

    let nextIndex;
    if (direction === 'previous') {
      nextIndex = currentIndex > 0 ? currentIndex - 1 : conversations.length - 1;
    } else {
      nextIndex = currentIndex < conversations.length - 1 ? currentIndex + 1 : 0;
    }

    const nextConversation = conversations[nextIndex] as HTMLElement;
    if (nextConversation) {
      nextConversation.click();
      nextConversation.focus();
    }
  };

  const scrollToTop = () => {
    const chatPanel = document.querySelector('[data-testid="chat-panel"]');
    if (chatPanel) {
      chatPanel.scrollTop = 0;
    }
  };

  const scrollToBottom = () => {
    const chatPanel = document.querySelector('[data-testid="chat-panel"]');
    if (chatPanel) {
      chatPanel.scrollTop = chatPanel.scrollHeight;
    }
  };

  return {
    navigateMessages,
    navigateConversations,
    scrollToTop,
    scrollToBottom,
  };
}

// Hook for responsive design helpers
export function useResponsive() {
  const [screenSize, setScreenSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  const [breakpoint, setBreakpoint] = useState<'sm' | 'md' | 'lg' | 'xl' | '2xl'>('lg');

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setScreenSize({ width, height });

      // Determine breakpoint
      if (width < 640) {
        setBreakpoint('sm');
      } else if (width < 768) {
        setBreakpoint('md');
      } else if (width < 1024) {
        setBreakpoint('lg');
      } else if (width < 1280) {
        setBreakpoint('xl');
      } else {
        setBreakpoint('2xl');
      }

      // Update mobile state in UI store
      const isMobile = width < 768;
      useUIStore.getState().setMobileView(isMobile);
    };

    handleResize(); // Initial call
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isMobile = breakpoint === 'sm';
  const isTablet = breakpoint === 'md';
  const isDesktop = ['lg', 'xl', '2xl'].includes(breakpoint);

  return {
    screenSize,
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
  };
}
