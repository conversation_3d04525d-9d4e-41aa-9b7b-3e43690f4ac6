import { useEffect, useState, useCallback } from 'react';
import { useConversationStore } from '../stores/conversationStore';
import { StorageService } from '../services/storage';
import { Conversation } from '../types';

export function useOfflineSync() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  
  const { conversations, setLoading, setError } = useConversationStore();

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      syncToCloud();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Sync conversations to IndexedDB whenever they change
  useEffect(() => {
    const syncToLocal = async () => {
      try {
        for (const conversation of conversations) {
          await StorageService.saveConversation(conversation);
        }
      } catch (error) {
        console.error('Failed to sync to local storage:', error);
      }
    };

    if (conversations.length > 0) {
      syncToLocal();
    }
  }, [conversations]);

  // Load conversations from IndexedDB on app start
  const loadFromLocal = useCallback(async () => {
    try {
      setLoading(true);
      const localConversations = await StorageService.getAllConversations();
      
      if (localConversations.length > 0) {
        // Update store with local data
        useConversationStore.setState({ conversations: localConversations });
      }
    } catch (error) {
      console.error('Failed to load from local storage:', error);
      setError('Failed to load conversations from local storage');
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  // Sync to cloud when online (mock implementation)
  const syncToCloud = useCallback(async () => {
    if (!isOnline || isSyncing) return;

    try {
      setIsSyncing(true);
      
      // In a real app, this would sync with your backend
      // For demo purposes, we'll just simulate the sync
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setLastSyncTime(new Date());
    } catch (error) {
      console.error('Failed to sync to cloud:', error);
      setError('Failed to sync with cloud');
    } finally {
      setIsSyncing(false);
    }
  }, [isOnline, isSyncing, setError]);

  // Manual sync trigger
  const manualSync = useCallback(async () => {
    if (isOnline) {
      await syncToCloud();
    } else {
      setError('Cannot sync while offline');
    }
  }, [isOnline, syncToCloud, setError]);

  // Get sync status
  const getSyncStatus = useCallback(() => {
    if (!isOnline) {
      return 'offline';
    } else if (isSyncing) {
      return 'syncing';
    } else if (lastSyncTime) {
      const timeDiff = Date.now() - lastSyncTime.getTime();
      if (timeDiff < 60000) { // Less than 1 minute
        return 'synced';
      } else {
        return 'stale';
      }
    } else {
      return 'never';
    }
  }, [isOnline, isSyncing, lastSyncTime]);

  return {
    isOnline,
    isSyncing,
    lastSyncTime,
    syncStatus: getSyncStatus(),
    loadFromLocal,
    syncToCloud,
    manualSync,
  };
}

// Hook for managing offline indicators
export function useOfflineIndicator() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
      
      // Hide offline message after 5 seconds
      setTimeout(() => setShowOfflineMessage(false), 5000);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return {
    isOnline,
    showOfflineMessage,
    dismissOfflineMessage: () => setShowOfflineMessage(false),
  };
}

// Hook for caching strategies
export function useCaching() {
  const [cacheSize, setCacheSize] = useState({ conversations: 0, messages: 0 });
  const [isClearing, setIsClearing] = useState(false);

  // Get cache size
  const updateCacheSize = useCallback(async () => {
    try {
      const size = await StorageService.getDatabaseSize();
      setCacheSize(size);
    } catch (error) {
      console.error('Failed to get cache size:', error);
    }
  }, []);

  // Clear cache
  const clearCache = useCallback(async () => {
    try {
      setIsClearing(true);
      await StorageService.clearAllConversations();
      await updateCacheSize();
    } catch (error) {
      console.error('Failed to clear cache:', error);
      throw error;
    } finally {
      setIsClearing(false);
    }
  }, [updateCacheSize]);

  // Optimize cache (remove old conversations)
  const optimizeCache = useCallback(async (maxConversations: number = 100) => {
    try {
      const conversations = await StorageService.getAllConversations();
      
      if (conversations.length > maxConversations) {
        // Sort by last updated and keep only the most recent
        const sorted = conversations.sort((a, b) => 
          b.updated_at.getTime() - a.updated_at.getTime()
        );
        
        const toDelete = sorted.slice(maxConversations);
        
        for (const conv of toDelete) {
          await StorageService.deleteConversation(conv.id);
        }
        
        await updateCacheSize();
      }
    } catch (error) {
      console.error('Failed to optimize cache:', error);
      throw error;
    }
  }, [updateCacheSize]);

  useEffect(() => {
    updateCacheSize();
  }, [updateCacheSize]);

  return {
    cacheSize,
    isClearing,
    updateCacheSize,
    clearCache,
    optimizeCache,
  };
}
