{"version": 3, "file": "index-prod.umd.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/extends.js", "../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../node_modules/memoize-one/dist/memoize-one.esm.js", "../src/timer.js", "../src/domHelpers.js", "../src/createGridComponent.js", "../src/VariableSizeGrid.js", "../src/createListComponent.js", "../src/VariableSizeList.js", "../src/FixedSizeGrid.js", "../src/FixedSizeList.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../src/shallowDiffers.js", "../src/areEqual.js", "../src/shouldComponentUpdate.js"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\ntype ItemType = 'column' | 'row';\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype ItemMetadataMap = { [index: number]: ItemMetadata };\ntype InstanceProps = {|\n  columnMetadataMap: ItemMetadataMap,\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  lastMeasuredColumnIndex: number,\n  lastMeasuredRowIndex: number,\n  rowMetadataMap: ItemMetadataMap,\n|};\n\nconst getEstimatedTotalHeight = (\n  { rowCount }: Props<any>,\n  { rowMetadataMap, estimatedRowHeight, lastMeasuredRowIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredRowIndex >= rowCount) {\n    lastMeasuredRowIndex = rowCount - 1;\n  }\n\n  if (lastMeasuredRowIndex >= 0) {\n    const itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getEstimatedTotalWidth = (\n  { columnCount }: Props<any>,\n  {\n    columnMetadataMap,\n    estimatedColumnWidth,\n    lastMeasuredColumnIndex,\n  }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredColumnIndex >= columnCount) {\n    lastMeasuredColumnIndex = columnCount - 1;\n  }\n\n  if (lastMeasuredColumnIndex >= 0) {\n    const itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getItemMetadata = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  let itemMetadataMap, itemSize, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    itemSize = ((props.columnWidth: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    itemSize = ((props.rowHeight: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = itemSize(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    if (itemType === 'column') {\n      instanceProps.lastMeasuredColumnIndex = index;\n    } else {\n      instanceProps.lastMeasuredRowIndex = index;\n    }\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  let itemMetadataMap, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      itemType,\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      itemType,\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(\n      itemType,\n      props,\n      middle,\n      instanceProps\n    ).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const itemCount = itemType === 'column' ? props.columnCount : props.rowCount;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(itemType, props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    itemType,\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getOffsetForIndexAndAlignment = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: InstanceProps,\n  scrollbarSize: number\n): number => {\n  const size = itemType === 'column' ? props.width : props.height;\n  const itemMetadata = getItemMetadata(itemType, props, index, instanceProps);\n\n  // Get estimated total size after ItemMetadata is computed,\n  // To ensure it reflects actual measurements instead of just estimates.\n  const estimatedTotalSize =\n    itemType === 'column'\n      ? getEstimatedTotalWidth(props, instanceProps)\n      : getEstimatedTotalHeight(props, instanceProps);\n\n  const maxOffset = Math.max(\n    0,\n    Math.min(estimatedTotalSize - size, itemMetadata.offset)\n  );\n  const minOffset = Math.max(\n    0,\n    itemMetadata.offset - size + scrollbarSize + itemMetadata.size\n  );\n\n  if (align === 'smart') {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      align = 'auto';\n    } else {\n      align = 'center';\n    }\n  }\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n    case 'end':\n      return minOffset;\n    case 'center':\n      return Math.round(minOffset + (maxOffset - minOffset) / 2);\n    case 'auto':\n    default:\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset;\n      } else if (minOffset > maxOffset) {\n        // Because we only take into account the scrollbar size when calculating minOffset\n        // this value can be larger than maxOffset when at the end of the list\n        return minOffset;\n      } else if (scrollOffset < minOffset) {\n        return minOffset;\n      } else {\n        return maxOffset;\n      }\n  }\n};\n\nconst VariableSizeGrid = createGridComponent({\n  getColumnOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('column', props, index, instanceProps).offset,\n\n  getColumnStartIndexForOffset: (\n    props: Props<any>,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('column', props, instanceProps, scrollLeft),\n\n  getColumnStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { columnCount, width } = props;\n\n    const itemMetadata = getItemMetadata(\n      'column',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollLeft + width;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < columnCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('column', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  getColumnWidth: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.columnMetadataMap[index].size,\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getOffsetForColumnAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'column',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getOffsetForRowAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'row',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getRowOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('row', props, index, instanceProps).offset,\n\n  getRowHeight: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.rowMetadataMap[index].size,\n\n  getRowStartIndexForOffset: (\n    props: Props<any>,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('row', props, instanceProps, scrollTop),\n\n  getRowStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { rowCount, height } = props;\n\n    const itemMetadata = getItemMetadata(\n      'row',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollTop + height;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < rowCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('row', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const {\n      estimatedColumnWidth,\n      estimatedRowHeight,\n    } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      columnMetadataMap: {},\n      estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n      estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredColumnIndex: -1,\n      lastMeasuredRowIndex: -1,\n      rowMetadataMap: {},\n    };\n\n    instance.resetAfterColumnIndex = (\n      columnIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ columnIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterRowIndex = (\n      rowIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ rowIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterIndices = ({\n      columnIndex,\n      rowIndex,\n      shouldForceUpdate = true,\n    }: {\n      columnIndex?: number,\n      rowIndex?: number,\n      shouldForceUpdate: boolean,\n    }) => {\n      if (typeof columnIndex === 'number') {\n        instanceProps.lastMeasuredColumnIndex = Math.min(\n          instanceProps.lastMeasuredColumnIndex,\n          columnIndex - 1\n        );\n      }\n      if (typeof rowIndex === 'number') {\n        instanceProps.lastMeasuredRowIndex = Math.min(\n          instanceProps.lastMeasuredRowIndex,\n          rowIndex - 1\n        );\n      }\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'function') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      } else if (typeof rowHeight !== 'function') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeGrid;\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount, layout } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      // The scrollbar size should be considered when scrolling an item into view, to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      // This is an edge case for lists; normally they only scroll in the dominant direction.\n      let scrollbarSize = 0;\n      if (this._outerRef) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (layout === 'vertical') {\n          scrollbarSize =\n            outerRef.scrollWidth > outerRef.clientWidth\n              ? getScrollbarSize()\n              : 0;\n        } else {\n          scrollbarSize =\n            outerRef.scrollHeight > outerRef.clientHeight\n              ? getScrollbarSize()\n              : 0;\n        }\n      }\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps,\n          scrollbarSize\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createListComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedItemSize: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype InstanceProps = {|\n  itemMetadataMap: { [index: number]: ItemMetadata },\n  estimatedItemSize: number,\n  lastMeasuredIndex: number,\n|};\n\nconst getItemMetadata = (\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  const { itemSize } = ((props: any): VariableSizeProps);\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = ((itemSize: any): itemSizeGetter)(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    instanceProps.lastMeasuredIndex = index;\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(props, middle, instanceProps).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const { itemCount } = props;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getEstimatedTotalSize = (\n  { itemCount }: Props<any>,\n  { itemMetadataMap, estimatedItemSize, lastMeasuredIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredItems = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredIndex >= itemCount) {\n    lastMeasuredIndex = itemCount - 1;\n  }\n\n  if (lastMeasuredIndex >= 0) {\n    const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n    totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\n\nconst VariableSizeList = createListComponent({\n  getItemOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata(props, index, instanceProps).offset,\n\n  getItemSize: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.itemMetadataMap[index].size,\n\n  getEstimatedTotalSize,\n\n  getOffsetForIndexAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    const { direction, height, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, index, instanceProps);\n\n    // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    const estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n\n    const maxOffset = Math.max(\n      0,\n      Math.min(estimatedTotalSize - size, itemMetadata.offset)\n    );\n    const minOffset = Math.max(\n      0,\n      itemMetadata.offset - size + itemMetadata.size + scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    props: Props<any>,\n    offset: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem(props, instanceProps, offset),\n\n  getStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, itemCount, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, startIndex, instanceProps);\n    const maxOffset = scrollOffset + size;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < itemCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata(props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const { estimatedItemSize } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      itemMetadataMap: {},\n      estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredIndex: -1,\n    };\n\n    instance.resetAfterIndex = (\n      index: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instanceProps.lastMeasuredIndex = Math.min(\n        instanceProps.lastMeasuredIndex,\n        index - 1\n      );\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'function') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeList;\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst FixedSizeGrid = createGridComponent({\n  getColumnOffset: ({ columnWidth }: Props<any>, index: number): number =>\n    index * ((columnWidth: any): number),\n\n  getColumnWidth: ({ columnWidth }: Props<any>, index: number): number =>\n    ((columnWidth: any): number),\n\n  getRowOffset: ({ rowHeight }: Props<any>, index: number): number =>\n    index * ((rowHeight: any): number),\n\n  getRowHeight: ({ rowHeight }: Props<any>, index: number): number =>\n    ((rowHeight: any): number),\n\n  getEstimatedTotalHeight: ({ rowCount, rowHeight }: Props<any>) =>\n    ((rowHeight: any): number) * rowCount,\n\n  getEstimatedTotalWidth: ({ columnCount, columnWidth }: Props<any>) =>\n    ((columnWidth: any): number) * columnCount,\n\n  getOffsetForColumnAndAlignment: (\n    { columnCount, columnWidth, width }: Props<any>,\n    columnIndex: number,\n    align: ScrollToAlign,\n    scrollLeft: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastColumnOffset = Math.max(\n      0,\n      columnCount * ((columnWidth: any): number) - width\n    );\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * ((columnWidth: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      columnIndex * ((columnWidth: any): number) -\n        width +\n        scrollbarSize +\n        ((columnWidth: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getOffsetForRowAndAlignment: (\n    { rowHeight, height, rowCount }: Props<any>,\n    rowIndex: number,\n    align: ScrollToAlign,\n    scrollTop: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastRowOffset = Math.max(\n      0,\n      rowCount * ((rowHeight: any): number) - height\n    );\n    const maxOffset = Math.min(\n      lastRowOffset,\n      rowIndex * ((rowHeight: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      rowIndex * ((rowHeight: any): number) -\n        height +\n        scrollbarSize +\n        ((rowHeight: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: (\n    { columnWidth, columnCount }: Props<any>,\n    scrollLeft: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        Math.floor(scrollLeft / ((columnWidth: any): number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, columnCount, width }: Props<any>,\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * ((columnWidth: any): number);\n    const numVisibleColumns = Math.ceil(\n      (width + scrollLeft - left) / ((columnWidth: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, rowCount }: Props<any>,\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(rowCount - 1, Math.floor(scrollTop / ((rowHeight: any): number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, rowCount, height }: Props<any>,\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * ((rowHeight: any): number);\n    const numVisibleRows = Math.ceil(\n      (height + scrollTop - top) / ((rowHeight: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        rowCount - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'number') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      }\n\n      if (typeof rowHeight !== 'number') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeGrid;\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\ntype InstanceProps = any;\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) -\n        size +\n        ((itemSize: any): number) +\n        scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "// @flow\n\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nexport default function shallowDiffers(prev: Object, next: Object): boolean {\n  for (let attribute in prev) {\n    if (!(attribute in next)) {\n      return true;\n    }\n  }\n  for (let attribute in next) {\n    if (prev[attribute] !== next[attribute]) {\n      return true;\n    }\n  }\n  return false;\n}\n", "// @flow\n\nimport shallowDiffers from './shallowDiffers';\n\n// Custom comparison function for React.memo().\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nexport default function areEqual(\n  prevProps: Object,\n  nextProps: Object\n): boolean {\n  const { style: prevStyle, ...prevRest } = prevProps;\n  const { style: nextStyle, ...nextRest } = nextProps;\n\n  return (\n    !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest)\n  );\n}\n", "// @flow\n\nimport areEqual from './areEqual';\nimport shallowDiffers from './shallowDiffers';\n\n// Custom shouldComponentUpdate for class components.\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nexport default function shouldComponentUpdate(\n  nextProps: Object,\n  nextState: Object\n): boolean {\n  return (\n    !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState)\n  );\n}\n"], "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "safeIsNaN", "Number", "isNaN", "value", "areInputsEqual", "newInputs", "lastInputs", "first", "second", "memoizeOne", "resultFn", "isEqual", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "now", "performance", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "requestAnimationFrame", "tick", "size", "getScrollbarSize", "recalculate", "div", "document", "createElement", "style", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "cachedRTLResult", "getRTLOffsetType", "outerDiv", "outerStyle", "direction", "innerDiv", "innerStyle", "scrollLeft", "defaultItemKey", "columnIndex", "data", "rowIndex", "createGridComponent", "getColumnOffset", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "getColumnWidth", "getEstimatedTotalHeight", "getEstimatedTotalWidth", "getOffsetForColumnAndAlignment", "getOffsetForRowAndAlignment", "getRowHeight", "getRowOffset", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "props", "_instanceProps", "_this", "_resetIsScrollingTimeoutId", "_outerRef", "state", "instance", "isScrolling", "horizontalScrollDirection", "initialScrollLeft", "scrollTop", "initialScrollTop", "scrollUpdateWasRequested", "verticalScrollDirection", "_callOnItemsRendered", "overscanColumnStartIndex", "overscanColumnStopIndex", "overscanRowStartIndex", "overscanRowStopIndex", "visibleColumnStartIndex", "visibleColumnStopIndex", "visibleRowStartIndex", "visibleRowStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "columnWidth", "rowHeight", "itemStyleCache", "_getItemStyleCache", "offset", "isRtl", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScroll", "event", "currentTarget", "clientHeight", "scrollHeight", "scrollWidth", "setState", "prevState", "calculatedScrollLeft", "Math", "max", "min", "calculatedScrollTop", "_resetIsScrollingDebounced", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps", "scrollTo", "scrollToItem", "align", "columnCount", "rowCount", "scrollbarSize", "estimatedTotalHeight", "horizontalScrollbarSize", "verticalScrollbarSize", "componentDidMount", "_callPropsCallbacks", "componentDidUpdate", "componentWillUnmount", "render", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_getHorizontalRangeToRender", "columnStartIndex", "columnStopIndex", "_getVerticalRangeToRender", "rowStartIndex", "rowStopIndex", "items", "push", "estimatedTotalWidth", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "overscanColumnCount", "overscanColumnsCount", "overscanCount", "overscanCountResolved", "startIndex", "stopIndex", "overscanBackward", "overscanForward", "overscanRowCount", "overscanRowsCount", "PureComponent", "defaultProps", "rowMetadataMap", "estimatedRowHeight", "lastMeasuredRowIndex", "totalSizeOfMeasuredRows", "itemMetadata", "columnMetadataMap", "estimatedColumnWidth", "lastMeasuredColumnIndex", "getItemMetadata", "itemType", "index", "instanceProps", "itemMetadataMap", "itemSize", "lastMeasuredIndex", "findNearestItem", "findNearestItemBinarySearch", "findNearestItemExponentialSearch", "high", "low", "middle", "floor", "currentOffset", "itemCount", "interval", "getOffsetForIndexAndAlignment", "scrollOffset", "estimatedTotalSize", "maxOffset", "minOffset", "round", "VariableSizeGrid", "resetAfterColumnIndex", "shouldForceUpdate", "resetAfterIndices", "resetAfterRowIndex", "forceUpdate", "createListComponent", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "scrollDirection", "initialScrollOffset", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "layout", "isHorizontal", "offsetHorizontal", "_onScrollHorizontal", "_onScrollVertical", "_getRangeToRender", "estimatedItemSize", "totalSizeOfMeasuredItems", "VariableSizeList", "resetAfterIndex", "FixedSizeGrid", "lastColumnOffset", "middleOffset", "ceil", "lastRowOffset", "numVisibleColumns", "numVisibleRows", "FixedSizeList", "lastItemOffset", "numVisibleItems", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "keys", "indexOf", "shallow<PERSON>iffers", "prev", "next", "attribute", "areEqual", "prevProps", "prevStyle", "prevRest", "nextStyle", "nextRest", "nextState"], "mappings": "4OAAe,SAASA,WACtBA,EAAWC,OAAOC,QAAU,SAAUC,OAC/B,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,KACrCG,EAASF,UAAUD,OAElB,IAAII,KAAOD,EACVN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAC/CL,EAAOK,GAAOD,EAAOC,WAKpBL,IAGOS,MAAMC,KAAMR,WCff,SAASS,EAAuBC,WAChC,IAATA,QACI,IAAIC,eAAe,oEAGpBD,ECLM,SAASE,EAAgBC,EAAGC,UACzCF,EAAkBhB,OAAOmB,gBAAkB,SAAyBF,EAAGC,UACrED,EAAEG,UAAYF,EACPD,IAGcA,EAAGC,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYR,OAAOwB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,IAAIG,EAAYC,OAAOC,OACnB,SAAkBC,SACU,iBAAVA,GAAsBA,GAAUA,GAWtD,SAASC,EAAeC,EAAWC,MAC3BD,EAAU1B,SAAW2B,EAAW3B,cACzB,MAEN,IAAIF,EAAI,EAAGA,EAAI4B,EAAU1B,OAAQF,OAbzB8B,EAcIF,EAAU5B,GAdP+B,EAcWF,EAAW7B,KAbtC8B,IAAUC,GAGVR,EAAUO,IAAUP,EAAUQ,WAWnB,EAfnB,IAAiBD,EAAOC,SAkBb,EAGX,SAASC,EAAWC,EAAUC,OAEtBC,OADY,IAAZD,IAAsBA,EAAUP,OAGhCS,EADAC,EAAW,GAEXC,GAAa,4BAETC,EAAU,GACLC,EAAK,EAAGA,EAAKvC,UAAUC,OAAQsC,IACpCD,EAAQC,GAAMvC,UAAUuC,UAExBF,GAAcH,IAAa1B,MAAQyB,EAAQK,EAASF,KAGxDD,EAAaH,EAASzB,MAAMC,KAAM8B,GAClCD,GAAa,EACbH,EAAW1B,KACX4B,EAAWE,GALAH,GChCnB,IAGMK,EAFmB,iBAAhBC,aAAuD,mBAApBA,YAAYD,IAGpD,kBAAMC,YAAYD,OAClB,kBAAME,KAAKF,OAMR,SAASG,EAAcC,GAC5BC,qBAAqBD,EAAUE,IAG1B,SAASC,EAAeC,EAAoBC,OAC3CC,EAAQV,QAURI,EAAuB,CAC3BE,GAAIK,gCATGC,IACHZ,IAAQU,GAASD,EACnBD,EAAS1C,KAAK,MAEdsC,EAAUE,GAAKK,sBAAsBC,cAQlCR,ECjCT,IAAIS,GAAgB,EAGb,SAASC,EAAiBC,eAAAA,IAAAA,GAAwB,IACzC,IAAVF,GAAeE,EAAa,KACxBC,EAAMC,SAASC,cAAc,OAC7BC,EAAQH,EAAIG,MAClBA,EAAMC,MAAQ,OACdD,EAAME,OAAS,OACfF,EAAMG,SAAW,SAEfL,SAASM,KAA6BC,YAAYR,GAEpDH,EAAOG,EAAIS,YAAcT,EAAIU,YAE3BT,SAASM,KAA6BI,YAAYX,UAG/CH,EAQT,IAAIe,EAAwC,KAQrC,SAASC,EAAiBd,eAAAA,IAAAA,GAAwB,GAC/B,OAApBa,GAA4Bb,EAAa,KACrCe,EAAWb,SAASC,cAAc,OAClCa,EAAaD,EAASX,MAC5BY,EAAWX,MAAQ,OACnBW,EAAWV,OAAS,OACpBU,EAAWT,SAAW,SACtBS,EAAWC,UAAY,UAEjBC,EAAWhB,SAASC,cAAc,OAClCgB,EAAaD,EAASd,aAC5Be,EAAWd,MAAQ,QACnBc,EAAWb,OAAS,QAEpBS,EAASN,YAAYS,GAEnBhB,SAASM,KAA6BC,YAAYM,GAEhDA,EAASK,WAAa,EACxBP,EAAkB,uBAElBE,EAASK,WAAa,EAEpBP,EAD0B,IAAxBE,EAASK,WACO,WAEA,sBAIpBlB,SAASM,KAA6BI,YAAYG,GAE7CF,SAGFA,ECwET,IAEMQ,EAAiB,gBAAGC,IAAAA,cAAaC,cAAMC,aAC5BF,GAeF,SAASG,WACtBC,IAAAA,gBACAC,IAAAA,6BACAC,IAAAA,gCACAC,IAAAA,eACAC,IAAAA,wBACAC,IAAAA,uBACAC,IAAAA,+BACAC,IAAAA,4BACAC,IAAAA,aACAC,IAAAA,aACAC,IAAAA,0BACAC,IAAAA,6BACAC,IAAAA,kBACAC,IAAAA,sCACAC,IAAAA,8CAgDcC,8BACJA,UA9BRC,eAAsBJ,EAAkBK,EAAKF,cAC7CG,2BAA+C,OAC/CC,mBAQAC,MAAe,CACbC,cACAC,aAAa,EACbC,0BAA2B,UAC3B7B,WAC0C,iBAAjCuB,EAAKF,MAAMS,kBACdP,EAAKF,MAAMS,kBACX,EACNC,UACyC,iBAAhCR,EAAKF,MAAMW,iBACdT,EAAKF,MAAMW,iBACX,EACNC,0BAA0B,EAC1BC,wBAAyB,aA8Q3BC,8BAUAA,qBAAuB/E,GACrB,SACEgF,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,UAEEpB,EAAKF,MAAMuB,gBAAgD,CAC3DR,yBAAAA,EACAC,wBAAAA,EACAC,sBAAAA,EACAC,qBAAAA,EACAC,wBAAAA,EACAC,uBAAAA,EACAC,qBAAAA,EACAC,oBAAAA,SAINE,uBAOAA,cAAgBzF,GACd,SACE4C,EACA+B,EACAF,EACAK,EACAD,UAEEV,EAAKF,MAAMyB,SAAkC,CAC7CjB,0BAAAA,EACA7B,WAAAA,EACA+B,UAAAA,EACAG,wBAAAA,EACAD,yBAAAA,SAwDNc,uBACAA,cAAgB,SAAC3C,EAAkBF,OAW7BlB,IAV0CuC,EAAKF,MAA3C2B,IAAAA,YAAanD,IAAAA,UAAWoD,IAAAA,UAE1BC,EAAiB3B,EAAK4B,mBAC1BhC,GAAyC6B,EACzC7B,GAAyCtB,EACzCsB,GAAyC8B,GAGrCzH,EAAS4E,MAAYF,KAGvBgD,EAAexH,eAAeF,GAChCwD,EAAQkE,EAAe1H,OAClB,KACC4H,EAAS9C,EACbiB,EAAKF,MACLnB,EACAqB,EAAKD,gBAED+B,EAAsB,QAAdxD,EACdqD,EAAe1H,GAAOwD,EAAQ,CAC5BsE,SAAU,WACVC,KAAMF,OAAQG,EAAYJ,EAC1BK,MAAOJ,EAAQD,OAASI,EACxBE,IAAK3C,EAAaQ,EAAKF,MAAOjB,EAAUmB,EAAKD,gBAC7CpC,OAAQ4B,EAAaS,EAAKF,MAAOjB,EAAUmB,EAAKD,gBAChDrC,MAAOwB,EAAec,EAAKF,MAAOnB,EAAaqB,EAAKD,wBAIjDtC,KAGTmE,4BACAA,mBAAqB/F,GAAW,SAACuG,EAAQC,EAASC,SAAc,QAkGhEC,UAAY,SAACC,SAQPA,EAAMC,cANRC,IAAAA,aACA1E,IAAAA,YACAS,IAAAA,WACA+B,IAAAA,UACAmC,IAAAA,aACAC,IAAAA,cAEGC,UAAS,SAAAC,MAEVA,EAAUrE,aAAeA,GACzBqE,EAAUtC,YAAcA,SAKjB,SAGDlC,EAAc0B,EAAKF,MAAnBxB,UAMJyE,EAAuBtE,KACT,QAAdH,SACMH,SACD,WACH4E,GAAwBtE,YAErB,sBACHsE,EAAuBH,EAAc5E,EAAcS,EAMzDsE,EAAuBC,KAAKC,IAC1B,EACAD,KAAKE,IAAIH,EAAsBH,EAAc5E,QAEzCmF,EAAsBH,KAAKC,IAC/B,EACAD,KAAKE,IAAI1C,EAAWmC,EAAeD,UAG9B,CACLrC,aAAa,EACbC,0BACEwC,EAAUrE,WAAaA,EAAa,UAAY,WAClDA,WAAYsE,EACZvC,UAAW2C,EACXxC,wBACEmC,EAAUtC,UAAYA,EAAY,UAAY,WAChDE,0BAA0B,KAE3BV,EAAKoD,+BAGVC,gBAAkB,SAACC,OACTC,EAAavD,EAAKF,MAAlByD,WAEHrD,UAAcoD,EAEK,mBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,iBAAbA,GACPA,EAASpJ,eAAe,aAExBoJ,EAASC,QAAUF,MAIvBF,2BAA6B,WACa,OAApCpD,EAAKC,4BACPxD,EAAcuD,EAAKC,8BAGhBA,2BAA6BpD,EAChCmD,EAAKyD,kBA/pB0B,QAoqBnCA,kBAAoB,aACbxD,2BAA6B,OAE7B4C,SAAS,CAAExC,aAAa,IAAS,aAG/BuB,oBAAoB,kBArlBtB8B,yBAAP,SACEC,EACAb,UAEAc,EAAoBD,EAAWb,GAC/BjD,EAAc8D,GACP,iCAGTE,SAAA,gBACEpF,IAAAA,WACA+B,IAAAA,eAKmByB,IAAfxD,IACFA,EAAauE,KAAKC,IAAI,EAAGxE,SAETwD,IAAdzB,IACFA,EAAYwC,KAAKC,IAAI,EAAGzC,SAGrBqC,UAAS,SAAAC,eACOb,IAAfxD,IACFA,EAAaqE,EAAUrE,iBAEPwD,IAAdzB,IACFA,EAAYsC,EAAUtC,WAItBsC,EAAUrE,aAAeA,GACzBqE,EAAUtC,YAAcA,EAEjB,KAGF,CACLF,0BACEwC,EAAUrE,WAAaA,EAAa,UAAY,WAClDA,WAAYA,EACZ+B,UAAWA,EACXE,0BAA0B,EAC1BC,wBACEmC,EAAUtC,UAAYA,EAAY,UAAY,cAEjDlG,KAAK8I,+BAGVU,aAAA,oBACEC,MAAAA,aAAQ,SACRpF,IAAAA,YACAE,IAAAA,WAMiDvE,KAAKwF,MAA9CkE,IAAAA,YAAarG,IAAAA,OAAQsG,IAAAA,SAAUvG,IAAAA,QACLpD,KAAK6F,MAA/B1B,IAAAA,WAAY+B,IAAAA,UACd0D,EAAgB9G,SAEF6E,IAAhBtD,IACFA,EAAcqE,KAAKC,IAAI,EAAGD,KAAKE,IAAIvE,EAAaqF,EAAc,UAE/C/B,IAAbpD,IACFA,EAAWmE,KAAKC,IAAI,EAAGD,KAAKE,IAAIrE,EAAUoF,EAAW,SAGjDE,EAAuBhF,EAC3B7E,KAAKwF,MACLxF,KAAKyF,gBAUDqE,EARsBhF,EAC1B9E,KAAKwF,MACLxF,KAAKyF,gBAOiBrC,EAAQwG,EAAgB,EAC1CG,EACJF,EAAuBxG,EAASuG,EAAgB,OAE7CL,SAAS,CACZpF,gBACkBwD,IAAhBtD,EACIU,EACE/E,KAAKwF,MACLnB,EACAoF,EACAtF,EACAnE,KAAKyF,eACLsE,GAEF5F,EACN+B,eACeyB,IAAbpD,EACIS,EACEhF,KAAKwF,MACLjB,EACAkF,EACAvD,EACAlG,KAAKyF,eACLqE,GAEF5D,OAIV8D,kBAAA,iBACkDhK,KAAKwF,MAA7CS,IAAAA,kBAAmBE,IAAAA,oBAEL,MAAlBnG,KAAK4F,UAAmB,KACpBqD,EAAajJ,KAAK4F,UACS,iBAAtBK,IACTgD,EAAS9E,WAAa8B,GAEQ,iBAArBE,IACT8C,EAAS/C,UAAYC,QAIpB8D,yBAGPC,mBAAA,eACUlG,EAAchE,KAAKwF,MAAnBxB,YACoDhE,KAAK6F,MAAzD1B,IAAAA,WAAY+B,IAAAA,eAAWE,0BAEmB,MAAlBpG,KAAK4F,UAAmB,KAIhDqD,EAAajJ,KAAK4F,aACN,QAAd5B,SACMH,SACD,WACHoF,EAAS9E,YAAcA,YAEpB,qBACH8E,EAAS9E,WAAaA,oBAGdT,EAA6BuF,EAA7BvF,YAAa4E,EAAgBW,EAAhBX,YACrBW,EAAS9E,WAAamE,EAAc5E,EAAcS,OAItD8E,EAAS9E,WAAauE,KAAKC,IAAI,EAAGxE,GAGpC8E,EAAS/C,UAAYwC,KAAKC,IAAI,EAAGzC,QAG9B+D,yBAGPE,qBAAA,WAC0C,OAApCnK,KAAK2F,4BACPxD,EAAcnC,KAAK2F,+BAIvByE,OAAA,iBAkBMpK,KAAKwF,MAhBP6E,IAAAA,SACAC,IAAAA,UACAZ,IAAAA,YACA1F,IAAAA,UACAX,IAAAA,OACAkH,IAAAA,SACAC,IAAAA,iBACAC,IAAAA,aACAC,IAAAA,aACAC,QAAAA,aAAUvG,IACVwG,IAAAA,iBACAC,IAAAA,aACAlB,IAAAA,SACAxG,IAAAA,MACA2H,IAAAA,eACA1H,IAAAA,MAEM2C,EAAgB/F,KAAK6F,MAArBE,cAKJ/F,KAAK+K,8BAFPC,OACAC,SAEoCjL,KAAKkL,4BAApCC,OAAeC,OAEhBC,EAAQ,MACV3B,EAAc,GAAKC,MAEnB,IAAIpF,EAAW4G,EACf5G,GAAY6G,EACZ7G,QAGE,IAAIF,EAAc2G,EAClB3G,GAAe4G,EACf5G,IAEAgH,EAAMC,KACJpI,gBAAcmH,EAAU,CACtBhG,YAAAA,EACAC,KAAMoG,EACN3E,YAAa+E,EAAiB/E,OAAc4B,EAC5ChI,IAAKgL,EAAQ,CAAEtG,YAAAA,EAAaC,KAAMoG,EAAUnG,SAAAA,IAC5CA,SAAAA,EACApB,MAAOnD,KAAKkH,cAAc3C,EAAUF,UASxCwF,EAAuBhF,EAC3B7E,KAAKwF,MACLxF,KAAKyF,gBAED8F,EAAsBzG,EAC1B9E,KAAKwF,MACLxF,KAAKyF,uBAGAvC,gBACL0H,GAAoBC,GAAgB,MACpC,CACEP,UAAAA,EACArD,SAAUjH,KAAKiI,UACfe,IAAKhJ,KAAK+I,gBACV5F,SACEsE,SAAU,WACVpE,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVkI,wBAAyB,QACzBC,WAAY,YACZzH,UAAAA,GACGb,IAGPD,gBAAcsH,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUgB,EACVrC,IAAKuB,EACLpH,MAAO,CACLE,OAAQwG,EACR6B,cAAe3F,EAAc,YAAS4B,EACtCvE,MAAOmI,SA+DftB,oBAAA,iBAC+DjK,KAAKwF,MAA1DkE,IAAAA,YAAa3C,IAAAA,gBAAiBE,IAAAA,SAAU0C,IAAAA,YAEjB,mBAApB5C,GACL2C,EAAc,GAAKC,EAAW,EAAG,OAM/B3J,KAAK+K,8BAJPxE,OACAC,OACAG,OACAC,SAOE5G,KAAKkL,4BAJPzE,OACAC,OACAG,OACAC,YAEGR,qBACHC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,MAKkB,mBAAbG,EAAyB,OAO9BjH,KAAK6F,MALPG,IAAAA,0BACA7B,IAAAA,WACA+B,IAAAA,UACAE,IAAAA,yBACAC,IAAAA,6BAEGW,cACH7C,EACA+B,EACAF,EACAK,EACAD,OA+CN2E,4BAAA,iBAOM/K,KAAKwF,MALPkE,IAAAA,YACAiC,IAAAA,oBACAC,IAAAA,qBACAC,IAAAA,cACAlC,IAAAA,WAE6D3J,KAAK6F,MAA5DG,IAAAA,0BAA2BD,IAAAA,YAAa5B,IAAAA,WAE1C2H,EACJH,GAAuBC,GAAwBC,GAAiB,KAE9C,IAAhBnC,GAAkC,IAAbC,QAChB,CAAC,EAAG,EAAG,EAAG,OAGboC,EAAarH,EACjB1E,KAAKwF,MACLrB,EACAnE,KAAKyF,gBAEDuG,EAAYrH,EAChB3E,KAAKwF,MACLuG,EACA5H,EACAnE,KAAKyF,gBAKDwG,EACHlG,GAA6C,aAA9BC,EAEZ,EADA0C,KAAKC,IAAI,EAAGmD,GAEZI,EACHnG,GAA6C,YAA9BC,EAEZ,EADA0C,KAAKC,IAAI,EAAGmD,SAGX,CACLpD,KAAKC,IAAI,EAAGoD,EAAaE,GACzBvD,KAAKC,IAAI,EAAGD,KAAKE,IAAIc,EAAc,EAAGsC,EAAYE,IAClDH,EACAC,MAIJd,0BAAA,iBAOMlL,KAAKwF,MALPkE,IAAAA,YACAmC,IAAAA,cACAM,IAAAA,iBACAC,IAAAA,kBACAzC,IAAAA,WAE0D3J,KAAK6F,MAAzDE,IAAAA,YAAaM,IAAAA,wBAAyBH,IAAAA,UAExC4F,EACJK,GAAoBC,GAAqBP,GAAiB,KAExC,IAAhBnC,GAAkC,IAAbC,QAChB,CAAC,EAAG,EAAG,EAAG,OAGboC,EAAa5G,EACjBnF,KAAKwF,MACLU,EACAlG,KAAKyF,gBAEDuG,EAAY5G,EAChBpF,KAAKwF,MACLuG,EACA7F,EACAlG,KAAKyF,gBAKDwG,EACHlG,GAA2C,aAA5BM,EAEZ,EADAqC,KAAKC,IAAI,EAAGmD,GAEZI,EACHnG,GAA2C,YAA5BM,EAEZ,EADAqC,KAAKC,IAAI,EAAGmD,SAGX,CACLpD,KAAKC,IAAI,EAAGoD,EAAaE,GACzBvD,KAAKC,IAAI,EAAGD,KAAKE,IAAIe,EAAW,EAAGqC,EAAYE,IAC/CH,EACAC,OArhBuBK,kBAKpBC,aAAe,CACpBtI,UAAW,MACX0G,cAAU/C,EACVmD,gBAAgB,KAqnBtB,IAAMxB,EAAsB,gBAExBe,WACArG,YACAX,SACAoH,eACAI,eACAe,uBACAC,gBACAO,oBACAhJ,QAEA0C,UC3yBEjB,EAA0B,kBAC5B8E,IAAAA,SACA4C,IAAAA,eAAgBC,IAAAA,mBAAoBC,IAAAA,qBAElCC,EAA0B,KAI1BD,GAAwB9C,IAC1B8C,EAAuB9C,EAAW,GAGhC8C,GAAwB,EAAG,KACvBE,EAAeJ,EAAeE,GACpCC,EAA0BC,EAAapF,OAASoF,EAAa9J,YAMxD6J,GAHoB/C,EAAW8C,EAAuB,GACLD,GAKpD1H,EAAyB,kBAC3B4E,IAAAA,YAEAkD,IAAAA,kBACAC,IAAAA,qBACAC,IAAAA,wBAGEJ,EAA0B,KAI1BI,GAA2BpD,IAC7BoD,EAA0BpD,EAAc,GAGtCoD,GAA2B,EAAG,KAC1BH,EAAeC,EAAkBE,GACvCJ,EAA0BC,EAAapF,OAASoF,EAAa9J,YAMxD6J,GAHoBhD,EAAcoD,EAA0B,GACXD,GAKpDE,EAAkB,SACtBC,EACAxH,EACAyH,EACAC,OAEIC,EAAiBC,EAAUC,KACd,WAAbL,GACFG,EAAkBD,EAAcN,kBAChCQ,EAAa5H,EAAM2B,YACnBkG,EAAoBH,EAAcJ,0BAElCK,EAAkBD,EAAcX,eAChCa,EAAa5H,EAAM4B,UACnBiG,EAAoBH,EAAcT,sBAGhCQ,EAAQI,EAAmB,KACzB9F,EAAS,KACT8F,GAAqB,EAAG,KACpBV,EAAeQ,EAAgBE,GACrC9F,EAASoF,EAAapF,OAASoF,EAAa9J,SAGzC,IAAItD,EAAI8N,EAAoB,EAAG9N,GAAK0N,EAAO1N,IAAK,KAC/CsD,EAAOuK,EAAS7N,GAEpB4N,EAAgB5N,GAAK,CACnBgI,OAAAA,EACA1E,KAAAA,GAGF0E,GAAU1E,EAGK,WAAbmK,EACFE,EAAcJ,wBAA0BG,EAExCC,EAAcT,qBAAuBQ,SAIlCE,EAAgBF,IAGnBK,EAAkB,SACtBN,EACAxH,EACA0H,EACA3F,OAEI4F,EAAiBE,QACJ,WAAbL,GACFG,EAAkBD,EAAcN,kBAChCS,EAAoBH,EAAcJ,0BAElCK,EAAkBD,EAAcX,eAChCc,EAAoBH,EAAcT,uBAIlCY,EAAoB,EAAIF,EAAgBE,GAAmB9F,OAAS,IAExCA,EAErBgG,EACLP,EACAxH,EACA0H,EACAG,EACA,EACA9F,GAMKiG,EACLR,EACAxH,EACA0H,EACAxE,KAAKC,IAAI,EAAG0E,GACZ9F,IAKAgG,EAA8B,SAClCP,EACAxH,EACA0H,EACAO,EACAC,EACAnG,QAEOmG,GAAOD,GAAM,KACZE,EAASD,EAAMhF,KAAKkF,OAAOH,EAAOC,GAAO,GACzCG,EAAgBd,EACpBC,EACAxH,EACAmI,EACAT,GACA3F,UAEEsG,IAAkBtG,SACboG,EACEE,EAAgBtG,EACzBmG,EAAMC,EAAS,EACNE,EAAgBtG,IACzBkG,EAAOE,EAAS,UAIhBD,EAAM,EACDA,EAAM,EAEN,GAILF,EAAmC,SACvCR,EACAxH,EACA0H,EACAD,EACA1F,WAEMuG,EAAyB,WAAbd,EAAwBxH,EAAMkE,YAAclE,EAAMmE,SAChEoE,EAAW,EAGbd,EAAQa,GACRf,EAAgBC,EAAUxH,EAAOyH,EAAOC,GAAe3F,OAASA,GAEhE0F,GAASc,EACTA,GAAY,SAGPR,EACLP,EACAxH,EACA0H,EACAxE,KAAKE,IAAIqE,EAAOa,EAAY,GAC5BpF,KAAKkF,MAAMX,EAAQ,GACnB1F,IAIEyG,EAAgC,SACpChB,EACAxH,EACAyH,EACAxD,EACAwE,EACAf,EACAtD,OAEM/G,EAAoB,WAAbmK,EAAwBxH,EAAMpC,MAAQoC,EAAMnC,OACnDsJ,EAAeI,EAAgBC,EAAUxH,EAAOyH,EAAOC,GAIvDgB,EACS,WAAblB,EACIlI,EAAuBU,EAAO0H,GAC9BrI,EAAwBW,EAAO0H,GAE/BiB,EAAYzF,KAAKC,IACrB,EACAD,KAAKE,IAAIsF,EAAqBrL,EAAM8J,EAAapF,SAE7C6G,EAAY1F,KAAKC,IACrB,EACAgE,EAAapF,OAAS1E,EAAO+G,EAAgB+C,EAAa9J,aAG9C,UAAV4G,IAEAA,EADEwE,GAAgBG,EAAYvL,GAAQoL,GAAgBE,EAAYtL,EAC1D,OAEA,UAIJ4G,OACD,eACI0E,MACJ,aACIC,MACJ,gBACI1F,KAAK2F,MAAMD,GAAaD,EAAYC,GAAa,OACrD,sBAECH,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEG,EAAYD,GAIZF,EAAeG,EADjBA,EAIAD,IAKTG,EAAmB9J,EAAoB,CAC3CC,gBAAiB,SACfe,EACAyH,EACAC,UACWH,EAAgB,SAAUvH,EAAOyH,EAAOC,GAAe3F,QAEpE7C,6BAA8B,SAC5Bc,EACArB,EACA+I,UACWI,EAAgB,SAAU9H,EAAO0H,EAAe/I,IAE7DQ,gCAAiC,SAC/Ba,EACAuG,EACA5H,EACA+I,WAEQxD,EAAuBlE,EAAvBkE,YAAatG,EAAUoC,EAAVpC,MAEfuJ,EAAeI,EACnB,SACAvH,EACAuG,EACAmB,GAEIiB,EAAYhK,EAAaf,EAE3BmE,EAASoF,EAAapF,OAASoF,EAAa9J,KAC5CmJ,EAAYD,EAETC,EAAYtC,EAAc,GAAKnC,EAAS4G,GAC7CnC,IACAzE,GAAUwF,EAAgB,SAAUvH,EAAOwG,EAAWkB,GAAerK,YAGhEmJ,GAGTpH,eAAgB,SACdY,EACAyH,EACAC,UACWA,EAAcN,kBAAkBK,GAAOpK,MAEpDgC,wBAAAA,EACAC,uBAAAA,EAEAC,+BAAgC,SAC9BS,EACAyH,EACAxD,EACAwE,EACAf,EACAtD,UAEAoE,EACE,SACAxI,EACAyH,EACAxD,EACAwE,EACAf,EACAtD,IAGJ5E,4BAA6B,SAC3BQ,EACAyH,EACAxD,EACAwE,EACAf,EACAtD,UAEAoE,EACE,MACAxI,EACAyH,EACAxD,EACAwE,EACAf,EACAtD,IAGJ1E,aAAc,SACZM,EACAyH,EACAC,UACWH,EAAgB,MAAOvH,EAAOyH,EAAOC,GAAe3F,QAEjEtC,aAAc,SACZO,EACAyH,EACAC,UACWA,EAAcX,eAAeU,GAAOpK,MAEjDsC,0BAA2B,SACzBK,EACAU,EACAgH,UACWI,EAAgB,MAAO9H,EAAO0H,EAAehH,IAE1Dd,6BAA8B,SAC5BI,EACAuG,EACA7F,EACAgH,WAEQvD,EAAqBnE,EAArBmE,SAAUtG,EAAWmC,EAAXnC,OAEZsJ,EAAeI,EACnB,MACAvH,EACAuG,EACAmB,GAEIiB,EAAYjI,EAAY7C,EAE1BkE,EAASoF,EAAapF,OAASoF,EAAa9J,KAC5CmJ,EAAYD,EAETC,EAAYrC,EAAW,GAAKpC,EAAS4G,GAC1CnC,IACAzE,GAAUwF,EAAgB,MAAOvH,EAAOwG,EAAWkB,GAAerK,YAG7DmJ,GAGT3G,2BAAkBG,EAAmBM,SAI7BN,EAEA0H,EAAgB,CACpBN,kBAAmB,GACnBC,uBANAA,sBA9Z8B,GAqa9BL,qBANAA,oBA/Z8B,GAsa9BM,yBAA0B,EAC1BL,sBAAuB,EACvBF,eAAgB,WAGlBzG,EAASyI,sBAAwB,SAC/BlK,EACAmK,YAAAA,IAAAA,GAA8B,GAE9B1I,EAAS2I,kBAAkB,CAAEpK,YAAAA,EAAamK,kBAAAA,KAG5C1I,EAAS4I,mBAAqB,SAC5BnK,EACAiK,YAAAA,IAAAA,GAA8B,GAE9B1I,EAAS2I,kBAAkB,CAAElK,SAAAA,EAAUiK,kBAAAA,KAGzC1I,EAAS2I,kBAAoB,gBAC3BpK,IAAAA,YACAE,IAAAA,aACAiK,kBAAAA,gBAM2B,iBAAhBnK,IACT6I,EAAcJ,wBAA0BpE,KAAKE,IAC3CsE,EAAcJ,wBACdzI,EAAc,IAGM,iBAAbE,IACT2I,EAAcT,qBAAuB/D,KAAKE,IACxCsE,EAAcT,qBACdlI,EAAW,IAQfuB,EAASwB,oBAAoB,GAEzBkH,GACF1I,EAAS6I,eAINzB,GAGT5H,uCAAuC,EAEvCC,cAAe,cAAG4B,cAAaC,aCzW3BhD,EAAiB,SAAC6I,EAAe3I,UAAc2I,GAatC,SAAS2B,WACtBC,IAAAA,cACAC,IAAAA,sBACAC,IAAAA,YACAf,IAAAA,8BACAgB,IAAAA,uBACAC,IAAAA,0BACA5J,IAAAA,kBACAC,IAAAA,sCACAC,IAAAA,8CAuCcC,8BACJA,UA3BRC,eAAsBJ,EAAkBK,EAAKF,cAC7CI,mBACAD,2BAA+C,OAU/CE,MAAe,CACbC,cACAC,aAAa,EACbmJ,gBAAiB,UACjBjB,aAC4C,iBAAnCvI,EAAKF,MAAM2J,oBACdzJ,EAAKF,MAAM2J,oBACX,EACN/I,0BAA0B,KAoN5BE,8BAMAA,qBAAuB/E,GACrB,SACE6N,EACAC,EACAC,EACAC,UAEE7J,EAAKF,MAAMuB,gBAAgD,CAC3DqI,mBAAAA,EACAC,kBAAAA,EACAC,kBAAAA,EACAC,iBAAAA,SAINvI,uBAKAA,cAAgBzF,GACd,SACE2N,EACAjB,EACA7H,UAEEV,EAAKF,MAAMyB,SAAkC,CAC7CiI,gBAAAA,EACAjB,aAAAA,EACA7H,yBAAAA,SAyCNc,uBACAA,cAAgB,SAAC+F,OASX9J,IARoCuC,EAAKF,MAArCxB,IAAAA,UAAWoJ,IAAAA,SAAUoC,IAAAA,OAEvBnI,EAAiB3B,EAAK4B,mBAC1BhC,GAAyC8H,EACzC9H,GAAyCkK,EACzClK,GAAyCtB,MAIvCqD,EAAexH,eAAeoN,GAChC9J,EAAQkE,EAAe4F,OAClB,KACC1F,EAASsH,EAAcnJ,EAAKF,MAAOyH,EAAOvH,EAAKD,gBAC/C5C,EAAOkM,EAAYrJ,EAAKF,MAAOyH,EAAOvH,EAAKD,gBAG3CgK,EACU,eAAdzL,GAAyC,eAAXwL,EAE1BhI,EAAsB,QAAdxD,EACR0L,EAAmBD,EAAelI,EAAS,EACjDF,EAAe4F,GAAS9J,EAAQ,CAC9BsE,SAAU,WACVC,KAAMF,OAAQG,EAAY+H,EAC1B9H,MAAOJ,EAAQkI,OAAmB/H,EAClCE,IAAM4H,EAAwB,EAATlI,EACrBlE,OAASoM,EAAsB,OAAP5M,EACxBO,MAAOqM,EAAe5M,EAAO,eAI1BM,KAGTmE,4BACAA,mBAAqB/F,GAAW,SAACuG,EAAQC,EAASC,SAAc,QAyChE2H,oBAAsB,SAACzH,SAC4BA,EAAMC,cAA/CzE,IAAAA,YAAaS,IAAAA,WAAYmE,IAAAA,cAC5BC,UAAS,SAAAC,MACRA,EAAUyF,eAAiB9J,SAItB,SAGDH,EAAc0B,EAAKF,MAAnBxB,UAEJiK,EAAe9J,KACD,QAAdH,SAKMH,SACD,WACHoK,GAAgB9J,YAEb,sBACH8J,EAAe3F,EAAc5E,EAAcS,SAMjD8J,EAAevF,KAAKC,IAClB,EACAD,KAAKE,IAAIqF,EAAc3F,EAAc5E,IAGhC,CACLqC,aAAa,EACbmJ,gBACE1G,EAAUyF,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACA7H,0BAA0B,KAE3BV,EAAKoD,+BAGV8G,kBAAoB,SAAC1H,SAC+BA,EAAMC,cAAhDC,IAAAA,aAAcC,IAAAA,aAAcnC,IAAAA,YAC/BqC,UAAS,SAAAC,MACRA,EAAUyF,eAAiB/H,SAItB,SAIH+H,EAAevF,KAAKC,IACxB,EACAD,KAAKE,IAAI1C,EAAWmC,EAAeD,UAG9B,CACLrC,aAAa,EACbmJ,gBACE1G,EAAUyF,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACA7H,0BAA0B,KAE3BV,EAAKoD,+BAGVC,gBAAkB,SAACC,OACTC,EAAavD,EAAKF,MAAlByD,WAEHrD,UAAcoD,EAEK,mBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,iBAAbA,GACPA,EAASpJ,eAAe,aAExBoJ,EAASC,QAAUF,MAIvBF,2BAA6B,WACa,OAApCpD,EAAKC,4BACPxD,EAAcuD,EAAKC,8BAGhBA,2BAA6BpD,EAChCmD,EAAKyD,kBAngB0B,QAwgBnCA,kBAAoB,aACbxD,2BAA6B,OAE7B4C,SAAS,CAAExC,aAAa,IAAS,aAG/BuB,oBAAoB,EAAG,qBA3czB8B,yBAAP,SACEC,EACAb,UAEAc,EAAoBD,EAAWb,GAC/BjD,EAAc8D,GACP,iCAGTE,SAAA,SAAS0E,GACPA,EAAevF,KAAKC,IAAI,EAAGsF,QAEtB1F,UAAS,SAAAC,UACRA,EAAUyF,eAAiBA,EACtB,KAEF,CACLiB,gBACE1G,EAAUyF,aAAeA,EAAe,UAAY,WACtDA,aAAcA,EACd7H,0BAA0B,KAE3BpG,KAAK8I,+BAGVU,aAAA,SAAayD,EAAexD,YAAAA,IAAAA,EAAuB,cACnBzJ,KAAKwF,MAA3BsI,IAAAA,UAAW0B,IAAAA,OACXvB,EAAiBjO,KAAK6F,MAAtBoI,aAERhB,EAAQvE,KAAKC,IAAI,EAAGD,KAAKE,IAAIqE,EAAOa,EAAY,QAK5ClE,EAAgB,KAChB5J,KAAK4F,UAAW,KACZqD,EAAajJ,KAAK4F,UAEtBgE,EADa,aAAX4F,EAEAvG,EAASX,YAAcW,EAASvF,YAC5BZ,IACA,EAGJmG,EAASZ,aAAeY,EAASb,aAC7BtF,IACA,OAILyG,SACHyE,EACEhO,KAAKwF,MACLyH,EACAxD,EACAwE,EACAjO,KAAKyF,eACLmE,OAKNI,kBAAA,iBACqDhK,KAAKwF,MAAhDxB,IAAAA,UAAWmL,IAAAA,oBAAqBK,IAAAA,UAEL,iBAAxBL,GAAsD,MAAlBnP,KAAK4F,UAAmB,KAC/DqD,EAAajJ,KAAK4F,UAEN,eAAd5B,GAAyC,eAAXwL,EAChCvG,EAAS9E,WAAagL,EAEtBlG,EAAS/C,UAAYiJ,OAIpBlF,yBAGPC,mBAAA,iBACgClK,KAAKwF,MAA3BxB,IAAAA,UAAWwL,IAAAA,SACgCxP,KAAK6F,MAAhDoI,IAAAA,kBAAc7H,0BAE4B,MAAlBpG,KAAK4F,UAAmB,KAChDqD,EAAajJ,KAAK4F,aAGN,eAAd5B,GAAyC,eAAXwL,KACd,QAAdxL,SAIMH,SACD,WACHoF,EAAS9E,YAAc8J,YAEpB,qBACHhF,EAAS9E,WAAa8J,oBAGdvK,EAA6BuF,EAA7BvF,YAAa4E,EAAgBW,EAAhBX,YACrBW,EAAS9E,WAAamE,EAAc5E,EAAcuK,OAItDhF,EAAS9E,WAAa8J,OAGxBhF,EAAS/C,UAAY+H,OAIpBhE,yBAGPE,qBAAA,WAC0C,OAApCnK,KAAK2F,4BACPxD,EAAcnC,KAAK2F,+BAIvByE,OAAA,iBAkBMpK,KAAKwF,MAhBP6E,IAAAA,SACAC,IAAAA,UACAtG,IAAAA,UACAX,IAAAA,OACAkH,IAAAA,SACAC,IAAAA,iBACAC,IAAAA,aACAqD,IAAAA,UACApD,IAAAA,aACAC,QAAAA,aAAUvG,IACVoL,IAAAA,OACA5E,IAAAA,iBACAC,IAAAA,aACA1H,IAAAA,MACA2H,IAAAA,eACA1H,IAAAA,MAEM2C,EAAgB/F,KAAK6F,MAArBE,YAGF0J,EACU,eAAdzL,GAAyC,eAAXwL,EAE1BvI,EAAWwI,EACbzP,KAAK2P,oBACL3P,KAAK4P,oBAEuB5P,KAAK6P,oBAA9B9D,OAAYC,OAEbX,EAAQ,MACVyC,EAAY,MACT,IAAIb,EAAQlB,EAAYkB,GAASjB,EAAWiB,IAC/C5B,EAAMC,KACJpI,gBAAcmH,EAAU,CACtB/F,KAAMoG,EACN/K,IAAKgL,EAAQsC,EAAOvC,GACpBuC,MAAAA,EACAlH,YAAa+E,EAAiB/E,OAAc4B,EAC5CxE,MAAOnD,KAAKkH,cAAc+F,UAQ5BiB,EAAqBY,EACzB9O,KAAKwF,MACLxF,KAAKyF,uBAGAvC,gBACL0H,GAAoBC,GAAgB,MACpC,CACEP,UAAAA,EACArD,SAAAA,EACA+B,IAAKhJ,KAAK+I,gBACV5F,SACEsE,SAAU,WACVpE,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVkI,wBAAyB,QACzBC,WAAY,YACZzH,UAAAA,GACGb,IAGPD,gBAAcsH,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUgB,EACVrC,IAAKuB,EACLpH,MAAO,CACLE,OAAQoM,EAAe,OAASvB,EAChCxC,cAAe3F,EAAc,YAAS4B,EACtCvE,MAAOqM,EAAevB,EAAqB,cA6CnDjE,oBAAA,cAC4C,mBAA/BjK,KAAKwF,MAAMuB,iBACE/G,KAAKwF,MAAnBsI,UACQ,EAAG,OAMb9N,KAAK6P,oBAJPT,OACAC,OACAC,OACAC,YAEGjJ,qBACH8I,EACAC,EACAC,EACAC,MAK6B,mBAAxBvP,KAAKwF,MAAMyB,SAAyB,OAKzCjH,KAAK6F,MAHPqJ,IAAAA,gBACAjB,IAAAA,aACA7H,IAAAA,8BAEGY,cACHkI,EACAjB,EACA7H,OAgDNyJ,kBAAA,iBACuC7P,KAAKwF,MAAlCsI,IAAAA,UAAWjC,IAAAA,gBACoC7L,KAAK6F,MAApDE,IAAAA,YAAamJ,IAAAA,gBAAiBjB,IAAAA,gBAEpB,IAAdH,QACK,CAAC,EAAG,EAAG,EAAG,OAGb/B,EAAaiD,EACjBhP,KAAKwF,MACLyI,EACAjO,KAAKyF,gBAEDuG,EAAYiD,EAChBjP,KAAKwF,MACLuG,EACAkC,EACAjO,KAAKyF,gBAKDwG,EACHlG,GAAmC,aAApBmJ,EAEZ,EADAxG,KAAKC,IAAI,EAAGkD,GAEZK,EACHnG,GAAmC,YAApBmJ,EAEZ,EADAxG,KAAKC,IAAI,EAAGkD,SAGX,CACLnD,KAAKC,IAAI,EAAGoD,EAAaE,GACzBvD,KAAKC,IAAI,EAAGD,KAAKE,IAAIkF,EAAY,EAAG9B,EAAYE,IAChDH,EACAC,OA/XuBK,kBAKpBC,aAAe,CACpBtI,UAAW,MACX0G,cAAU/C,EACV6H,OAAQ,WACR3D,cAAe,EACff,gBAAgB,KA4etB,IAAMxB,EAAsB,gBAExBe,WACArG,YACAX,SACAmM,SACA/E,eACAI,eACAzH,QAEA0C,UCroBEiH,EAAkB,SACtBvH,EACAyH,EACAC,OAEQE,EAAe5H,EAAf4H,SACAD,EAAuCD,EAAvCC,gBAAiBE,EAAsBH,EAAtBG,qBAErBJ,EAAQI,EAAmB,KACzB9F,EAAS,KACT8F,GAAqB,EAAG,KACpBV,EAAeQ,EAAgBE,GACrC9F,EAASoF,EAAapF,OAASoF,EAAa9J,SAGzC,IAAItD,EAAI8N,EAAoB,EAAG9N,GAAK0N,EAAO1N,IAAK,KAC/CsD,EAASuK,EAAgC7N,GAE7C4N,EAAgB5N,GAAK,CACnBgI,OAAAA,EACA1E,KAAAA,GAGF0E,GAAU1E,EAGZqK,EAAcG,kBAAoBJ,SAG7BE,EAAgBF,IAmCnBM,EAA8B,SAClC/H,EACA0H,EACAO,EACAC,EACAnG,QAEOmG,GAAOD,GAAM,KACZE,EAASD,EAAMhF,KAAKkF,OAAOH,EAAOC,GAAO,GACzCG,EAAgBd,EAAgBvH,EAAOmI,EAAQT,GAAe3F,UAEhEsG,IAAkBtG,SACboG,EACEE,EAAgBtG,EACzBmG,EAAMC,EAAS,EACNE,EAAgBtG,IACzBkG,EAAOE,EAAS,UAIhBD,EAAM,EACDA,EAAM,EAEN,GAILF,EAAmC,SACvChI,EACA0H,EACAD,EACA1F,WAEQuG,EAActI,EAAdsI,UACJC,EAAW,EAGbd,EAAQa,GACRf,EAAgBvH,EAAOyH,EAAOC,GAAe3F,OAASA,GAEtD0F,GAASc,EACTA,GAAY,SAGPR,EACL/H,EACA0H,EACAxE,KAAKE,IAAIqE,EAAOa,EAAY,GAC5BpF,KAAKkF,MAAMX,EAAQ,GACnB1F,IAIEuH,EAAwB,kBAC1BhB,IAAAA,UACAX,IAAAA,gBAAiB2C,IAAAA,kBAAmBzC,IAAAA,kBAElC0C,EAA2B,KAI3B1C,GAAqBS,IACvBT,EAAoBS,EAAY,GAG9BT,GAAqB,EAAG,KACpBV,EAAeQ,EAAgBE,GACrC0C,EAA2BpD,EAAapF,OAASoF,EAAa9J,YAMzDkN,GAHoBjC,EAAYT,EAAoB,GACHyC,GAKpDE,EAAmBpB,EAAoB,CAC3CC,cAAe,SACbrJ,EACAyH,EACAC,UACWH,EAAgBvH,EAAOyH,EAAOC,GAAe3F,QAE1DwH,YAAa,SACXvJ,EACAyH,EACAC,UACWA,EAAcC,gBAAgBF,GAAOpK,MAElDiM,sBAAAA,EAEAd,8BAA+B,SAC7BxI,EACAyH,EACAxD,EACAwE,EACAf,EACAtD,OAEQ5F,EAAqCwB,EAArCxB,UAAWX,EAA0BmC,EAA1BnC,OAAQmM,EAAkBhK,EAAlBgK,OAAQpM,EAAUoC,EAAVpC,MAI7BP,EAD6B,eAAdmB,GAAyC,eAAXwL,EACpBpM,EAAQC,EACjCsJ,EAAeI,EAAgBvH,EAAOyH,EAAOC,GAI7CgB,EAAqBY,EAAsBtJ,EAAO0H,GAElDiB,EAAYzF,KAAKC,IACrB,EACAD,KAAKE,IAAIsF,EAAqBrL,EAAM8J,EAAapF,SAE7C6G,EAAY1F,KAAKC,IACrB,EACAgE,EAAapF,OAAS1E,EAAO8J,EAAa9J,KAAO+G,UAGrC,UAAVH,IAKAA,EAHAwE,GAAgBG,EAAYvL,GAC5BoL,GAAgBE,EAAYtL,EAEpB,OAEA,UAIJ4G,OACD,eACI0E,MACJ,aACIC,MACJ,gBACI1F,KAAK2F,MAAMD,GAAaD,EAAYC,GAAa,OACrD,sBAECH,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEA,EAAeG,EACjBA,EAEAD,IAKfa,uBAAwB,SACtBxJ,EACA+B,EACA2F,UAxLoB,SACtB1H,EACA0H,EACA3F,OAEQ4F,EAAuCD,EAAvCC,gBAAiBE,EAAsBH,EAAtBG,yBAGvBA,EAAoB,EAAIF,EAAgBE,GAAmB9F,OAAS,IAExCA,EAErBgG,EACL/H,EACA0H,EACAG,EACA,EACA9F,GAMKiG,EACLhI,EACA0H,EACAxE,KAAKC,IAAI,EAAG0E,GACZ9F,GA8JS+F,CAAgB9H,EAAO0H,EAAe3F,IAEnD0H,0BAA2B,SACzBzJ,EACAuG,EACAkC,EACAf,WAEQlJ,EAAgDwB,EAAhDxB,UAAWX,EAAqCmC,EAArCnC,OAAQyK,EAA6BtI,EAA7BsI,UAAW0B,EAAkBhK,EAAlBgK,OAAQpM,EAAUoC,EAAVpC,MAIxCP,EAD6B,eAAdmB,GAAyC,eAAXwL,EACpBpM,EAAQC,EACjCsJ,EAAeI,EAAgBvH,EAAOuG,EAAYmB,GAClDiB,EAAYF,EAAepL,EAE7B0E,EAASoF,EAAapF,OAASoF,EAAa9J,KAC5CmJ,EAAYD,EAETC,EAAY8B,EAAY,GAAKvG,EAAS4G,GAC3CnC,IACAzE,GAAUwF,EAAgBvH,EAAOwG,EAAWkB,GAAerK,YAGtDmJ,GAGT3G,2BAAkBG,EAAmBM,OAG7BoH,EAAgB,CACpBC,gBAAiB,GACjB2C,kBAJ8BtK,EAAxBsK,mBAxQwB,GA6Q9BzC,mBAAoB,UAGtBvH,EAASmK,gBAAkB,SACzBhD,EACAuB,YAAAA,IAAAA,GAA8B,GAE9BtB,EAAcG,kBAAoB3E,KAAKE,IACrCsE,EAAcG,kBACdJ,EAAQ,GAOVnH,EAASwB,oBAAoB,GAEzBkH,GACF1I,EAAS6I,eAINzB,GAGT5H,uCAAuC,EAEvCC,cAAe,cAAG6H,YCzSd8C,EAAgB1L,EAAoB,CACxCC,gBAAiB,WAA8BwI,UAC7CA,IADkB9F,aAGpBvC,eAAgB,WAA8BqI,YAA3B9F,aAGnBjC,aAAc,WAA4B+H,UACxCA,IADe7F,WAGjBnC,aAAc,WAA4BgI,YAAzB7F,WAGjBvC,wBAAyB,gBAAG8E,IAAAA,kBAAUvC,UACPuC,GAE/B7E,uBAAwB,gBAAG4E,IAAAA,qBAAavC,YACPuC,GAEjC3E,+BAAgC,WAE9BV,EACAoF,EACAtF,EACA+I,EACAtD,OALEF,IAAAA,YAAavC,IAAAA,YAAa/D,IAAAA,MAOtB+M,EAAmBzH,KAAKC,IAC5B,EACAe,EAAgBvC,EAA6B/D,GAEzC+K,EAAYzF,KAAKE,IACrBuH,EACA9L,EAAgB8C,GAEZiH,EAAY1F,KAAKC,IACrB,EACAtE,EAAgB8C,EACd/D,EACAwG,EACEzC,UAGQ,UAAVsC,IAEAA,EADEtF,GAAciK,EAAYhL,GAASe,GAAcgK,EAAY/K,EACvD,OAEA,UAIJqG,OACD,eACI0E,MACJ,aACIC,MACJ,aAGGgC,EAAe1H,KAAK2F,MACxBD,GAAaD,EAAYC,GAAa,UAEpCgC,EAAe1H,KAAK2H,KAAKjN,EAAQ,GAC5B,EACEgN,EAAeD,EAAmBzH,KAAKkF,MAAMxK,EAAQ,GACvD+M,EAEAC,MAEN,sBAECjM,GAAciK,GAAajK,GAAcgK,EACpChK,EACEiK,EAAYD,GAIZhK,EAAaiK,EADfA,EAIAD,IAKfnJ,4BAA6B,WAE3BT,EACAkF,EACAvD,EACAgH,EACAtD,OALExC,IAAAA,UAAW/D,IAAAA,OAAQsG,IAAAA,SAOf2G,EAAgB5H,KAAKC,IACzB,EACAgB,EAAavC,EAA2B/D,GAEpC8K,EAAYzF,KAAKE,IACrB0H,EACA/L,EAAa6C,GAETgH,EAAY1F,KAAKC,IACrB,EACApE,EAAa6C,EACX/D,EACAuG,EACExC,UAGQ,UAAVqC,IAEAA,EADEvD,GAAakI,EAAY/K,GAAU6C,GAAaiI,EAAY9K,EACtD,OAEA,UAIJoG,OACD,eACI0E,MACJ,aACIC,MACJ,aAGGgC,EAAe1H,KAAK2F,MACxBD,GAAaD,EAAYC,GAAa,UAEpCgC,EAAe1H,KAAK2H,KAAKhN,EAAS,GAC7B,EACE+M,EAAeE,EAAgB5H,KAAKkF,MAAMvK,EAAS,GACrDiN,EAEAF,MAEN,sBAEClK,GAAakI,GAAalI,GAAaiI,EAClCjI,EACEkI,EAAYD,GAIZjI,EAAYkI,EADdA,EAIAD,IAKfzJ,6BAA8B,WAE5BP,OADEgD,IAAAA,YAAauC,IAAAA,mBAGfhB,KAAKC,IACH,EACAD,KAAKE,IACHc,EAAc,EACdhB,KAAKkF,MAAMzJ,EAAegD,MAIhCxC,gCAAiC,WAE/BoH,EACA5H,OAFEgD,IAAAA,YAAauC,IAAAA,YAAatG,IAAAA,MAItBsE,EAAOqE,EAAe5E,EACtBoJ,EAAoB7H,KAAK2H,MAC5BjN,EAAQe,EAAauD,GAAUP,UAE3BuB,KAAKC,IACV,EACAD,KAAKE,IACHc,EAAc,EACdqC,EAAawE,EAAoB,KAKvCpL,0BAA2B,WAEzBe,OADEkB,IAAAA,UAAWuC,IAAAA,gBAGbjB,KAAKC,IACH,EACAD,KAAKE,IAAIe,EAAW,EAAGjB,KAAKkF,MAAM1H,EAAckB,MAGpDhC,6BAA8B,WAE5B2G,EACA7F,OAFEkB,IAAAA,UAAWuC,IAAAA,SAAUtG,IAAAA,OAIjBwE,EAAMkE,EAAe3E,EACrBoJ,EAAiB9H,KAAK2H,MACzBhN,EAAS6C,EAAY2B,GAAST,UAE1BsB,KAAKC,IACV,EACAD,KAAKE,IACHe,EAAW,EACXoC,EAAayE,EAAiB,KAKpCnL,2BAAkBG,KAIlBF,uCAAuC,EAEvCC,cAAe,cAAG4B,cAAaC,aCpN3BqJ,EAAgB7B,EAAoB,CACxCC,cAAe,WAA2B5B,UACxCA,IADgBG,UAGlB2B,YAAa,WAA2B9B,YAAxBG,UAGhB0B,sBAAuB,gBAAGhB,IAAAA,mBAAWV,SACPU,GAE9BE,8BAA+B,WAE7Bf,EACAxD,EACAwE,EACAf,EACAtD,OALE5F,IAAAA,UAAWX,IAAAA,OAAQyK,IAAAA,UAAWV,IAAAA,SAAUoC,IAAAA,OAAQpM,IAAAA,MAS5CP,EAD6B,eAAdmB,GAAyC,eAAXwL,EACpBpM,EAAQC,EACjCqN,EAAiBhI,KAAKC,IAC1B,EACAmF,EAAcV,EAA0BvK,GAEpCsL,EAAYzF,KAAKE,IACrB8H,EACAzD,EAAUG,GAENgB,EAAY1F,KAAKC,IACrB,EACAsE,EAAUG,EACRvK,EACEuK,EACFxD,UAGU,UAAVH,IAKAA,EAHAwE,GAAgBG,EAAYvL,GAC5BoL,GAAgBE,EAAYtL,EAEpB,OAEA,UAIJ4G,OACD,eACI0E,MACJ,aACIC,MACJ,aAGGgC,EAAe1H,KAAK2F,MACxBD,GAAaD,EAAYC,GAAa,UAEpCgC,EAAe1H,KAAK2H,KAAKxN,EAAO,GAC3B,EACEuN,EAAeM,EAAiBhI,KAAKkF,MAAM/K,EAAO,GACpD6N,EAEAN,MAGN,sBAECnC,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEA,EAAeG,EACjBA,EAEAD,IAKfa,uBAAwB,WAEtBzH,OADEuG,IAAAA,UAAWV,IAAAA,gBAGb1E,KAAKC,IACH,EACAD,KAAKE,IAAIkF,EAAY,EAAGpF,KAAKkF,MAAMrG,EAAW6F,MAGlD6B,0BAA2B,WAEzBlD,EACAkC,OAFEjK,IAAAA,UAAWX,IAAAA,OAAQyK,IAAAA,UAAWV,IAAAA,SAAUoC,IAAAA,OAAQpM,IAAAA,MAM5CmE,EAASwE,EAAeqB,EACxBvK,EAF6B,eAAdmB,GAAyC,eAAXwL,EAEpBpM,EAAQC,EACjCsN,EAAkBjI,KAAK2H,MAC1BxN,EAAOoL,EAAe1G,GAAY6F,UAE9B1E,KAAKC,IACV,EACAD,KAAKE,IACHkF,EAAY,EACZ/B,EAAa4E,EAAkB,KAKrCtL,2BAAkBG,KAIlBF,uCAAuC,EAEvCC,cAAe,cAAG6H,YC3HL,SAASwD,EAA8BlR,EAAQmR,MAC9C,MAAVnR,EAAgB,MAAO,OAGvBC,EAAKJ,EAFLD,EAAS,GACTwR,EAAa1R,OAAO2R,KAAKrR,OAGxBH,EAAI,EAAGA,EAAIuR,EAAWrR,OAAQF,IACjCI,EAAMmR,EAAWvR,GACbsR,EAASG,QAAQrR,IAAQ,IAC7BL,EAAOK,GAAOD,EAAOC,WAGhBL,ECRM,SAAS2R,EAAeC,EAAcC,OAC9C,IAAIC,KAAaF,OACdE,KAAaD,UACV,MAGN,IAAIC,KAAaD,KAChBD,EAAKE,KAAeD,EAAKC,UACpB,SAGJ,8BCRM,SAASC,EACtBC,EACAjI,OAEekI,EAA2BD,EAAlCnO,MAAqBqO,IAAaF,KAC3BG,EAA2BpI,EAAlClG,MAAqBuO,IAAarI,YAGvC4H,EAAeM,EAAWE,KAAeR,EAAeO,EAAUE,sHCPxD,SACbrI,EACAsI,UAGGN,EAASrR,KAAKwF,MAAO6D,IAAc4H,EAAejR,KAAK6F,MAAO8L"}