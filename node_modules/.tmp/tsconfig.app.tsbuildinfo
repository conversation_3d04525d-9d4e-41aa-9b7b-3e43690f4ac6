{"root": ["../../src/App.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/chat/MessageBubble.tsx", "../../src/components/chat/MessageInput.tsx", "../../src/components/chat/StreamingRenderer.tsx", "../../src/components/layout/ChatApp.tsx", "../../src/components/layout/ChatPanel.tsx", "../../src/components/layout/Sidebar.tsx", "../../src/components/modals/SettingsModal.tsx", "../../src/components/ui/AuthGate.tsx", "../../src/components/ui/ErrorBoundary.tsx", "../../src/components/ui/LoadingSpinner.tsx", "../../src/components/ui/OfflineIndicator.tsx", "../../src/components/ui/VirtualizedMessageList.tsx", "../../src/hooks/useAccessibility.ts", "../../src/hooks/useChat.ts", "../../src/hooks/useKeyboardShortcuts.ts", "../../src/hooks/useLocalStorage.ts", "../../src/hooks/useOfflineSync.ts", "../../src/hooks/usePerformance.ts", "../../src/services/api.ts", "../../src/services/storage.ts", "../../src/services/streaming.ts", "../../src/stores/conversationStore.ts", "../../src/stores/uiStore.ts", "../../src/stores/userStore.ts", "../../src/types/index.ts", "../../src/utils/index.ts"], "version": "5.8.3"}