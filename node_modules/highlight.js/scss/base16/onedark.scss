pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: OneDark
  Author: <PERSON><PERSON> (http://github.com/tilal6991)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme onedark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #282c34  Default Background
base01  #353b45  Lighter Background (Used for status bars, line number and folding marks)
base02  #3e4451  Selection Background
base03  #545862  Comments, Invisibles, Line Highlighting
base04  #565c64  Dark Foreground (Used for status bars)
base05  #abb2bf  Default Foreground, Caret, Delimiters, Operators
base06  #b6bdca  Light Foreground (Not often used)
base07  #c8ccd4  Light Background (Not often used)
base08  #e06c75  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d19a66  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #e5c07b  Classes, Markup Bold, Search Text Background
base0B  #98c379  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #56b6c2  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #61afef  Functions, Methods, Attribute IDs, Headings
base0E  #c678dd  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #be5046  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #abb2bf;
  background: #282c34
}
.hljs::selection,
.hljs ::selection {
  background-color: #3e4451;
  color: #abb2bf
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #545862 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #545862
}
/* base04 - #565c64 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #565c64
}
/* base05 - #abb2bf -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #abb2bf
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e06c75
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d19a66
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #e5c07b
}
.hljs-strong {
  font-weight: bold;
  color: #e5c07b
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #98c379
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #56b6c2
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #61afef
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #c678dd
}
.hljs-emphasis {
  color: #c678dd;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #be5046
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}