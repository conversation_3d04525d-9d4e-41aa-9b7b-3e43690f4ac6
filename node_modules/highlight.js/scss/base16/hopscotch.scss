pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Hopscotch
  Author: Jan <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme hopscotch
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #322931  Default Background
base01  #433b42  Lighter Background (Used for status bars, line number and folding marks)
base02  #5c545b  Selection Background
base03  #797379  Comments, Invisibles, Line Highlighting
base04  #989498  Dark Foreground (Used for status bars)
base05  #b9b5b8  Default Foreground, Caret, Delimiters, Operators
base06  #d5d3d5  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #dd464c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #fd8b19  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fdcc59  Classes, Markup Bold, Search Text Background
base0B  #8fc13e  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #149b93  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #1290bf  Functions, Methods, Attribute IDs, Headings
base0E  #c85e7c  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b33508  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #b9b5b8;
  background: #322931
}
.hljs::selection,
.hljs ::selection {
  background-color: #5c545b;
  color: #b9b5b8
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #797379 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #797379
}
/* base04 - #989498 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #989498
}
/* base05 - #b9b5b8 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #b9b5b8
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #dd464c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #fd8b19
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fdcc59
}
.hljs-strong {
  font-weight: bold;
  color: #fdcc59
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #8fc13e
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #149b93
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #1290bf
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #c85e7c
}
.hljs-emphasis {
  color: #c85e7c;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b33508
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}