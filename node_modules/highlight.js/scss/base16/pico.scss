pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Pico
  Author: PICO-8 (http://www.lexaloffle.com/pico-8.php)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme pico
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #000000  Default Background
base01  #1d2b53  Lighter Background (Used for status bars, line number and folding marks)
base02  #7e2553  Selection Background
base03  #008751  Comments, Invisibles, Line Highlighting
base04  #ab5236  Dark Foreground (Used for status bars)
base05  #5f574f  Default Foreground, Caret, Delimiters, Operators
base06  #c2c3c7  Light Foreground (Not often used)
base07  #fff1e8  Light Background (Not often used)
base08  #ff004d  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ffa300  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fff024  Classes, Markup Bold, Search Text Background
base0B  #00e756  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #29adff  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #83769c  Functions, Methods, Attribute IDs, Headings
base0E  #ff77a8  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #ffccaa  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #5f574f;
  background: #000000
}
.hljs::selection,
.hljs ::selection {
  background-color: #7e2553;
  color: #5f574f
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #008751 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #008751
}
/* base04 - #ab5236 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #ab5236
}
/* base05 - #5f574f -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #5f574f
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ff004d
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ffa300
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fff024
}
.hljs-strong {
  font-weight: bold;
  color: #fff024
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #00e756
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #29adff
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #83769c
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ff77a8
}
.hljs-emphasis {
  color: #ff77a8;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #ffccaa
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}