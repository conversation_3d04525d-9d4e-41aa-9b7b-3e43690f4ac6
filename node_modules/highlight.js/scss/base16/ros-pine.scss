pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: <PERSON><PERSON><PERSON>
  Author: <PERSON> <<EMAIL>>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme ros-pine
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #191724  Default Background
base01  #1f1d2e  Lighter Background (Used for status bars, line number and folding marks)
base02  #26233a  Selection Background
base03  #555169  Comments, Invisibles, Line Highlighting
base04  #6e6a86  Dark Foreground (Used for status bars)
base05  #e0def4  Default Foreground, Caret, Delimiters, Operators
base06  #f0f0f3  Light Foreground (Not often used)
base07  #c5c3ce  Light Background (Not often used)
base08  #e2e1e7  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #eb6f92  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f6c177  Classes, Markup Bold, Search Text Background
base0B  #ebbcba  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #31748f  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #9ccfd8  Functions, Methods, Attribute IDs, Headings
base0E  #c4a7e7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e5e5e5  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e0def4;
  background: #191724
}
.hljs::selection,
.hljs ::selection {
  background-color: #26233a;
  color: #e0def4
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #555169 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #555169
}
/* base04 - #6e6a86 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #6e6a86
}
/* base05 - #e0def4 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e0def4
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #e2e1e7
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #eb6f92
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f6c177
}
.hljs-strong {
  font-weight: bold;
  color: #f6c177
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #ebbcba
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #31748f
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #9ccfd8
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #c4a7e7
}
.hljs-emphasis {
  color: #c4a7e7;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e5e5e5
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}