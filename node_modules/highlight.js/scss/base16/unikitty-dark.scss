pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Unikitty Dark
  Author: <PERSON> (@joshwlewis)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme unikitty-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #2e2a31  Default Background
base01  #4a464d  Lighter Background (Used for status bars, line number and folding marks)
base02  #666369  Selection Background
base03  #838085  Comments, Invisibles, Line Highlighting
base04  #9f9da2  Dark Foreground (Used for status bars)
base05  #bcbabe  Default Foreground, Caret, Delimiters, Operators
base06  #d8d7da  Light Foreground (Not often used)
base07  #f5f4f7  Light Background (Not often used)
base08  #d8137f  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d65407  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #dc8a0e  Classes, Markup Bold, Search Text Background
base0B  #17ad98  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #149bda  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #796af5  Functions, Methods, Attribute IDs, Headings
base0E  #bb60ea  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #c720ca  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #bcbabe;
  background: #2e2a31
}
.hljs::selection,
.hljs ::selection {
  background-color: #666369;
  color: #bcbabe
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #838085 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #838085
}
/* base04 - #9f9da2 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #9f9da2
}
/* base05 - #bcbabe -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #bcbabe
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d8137f
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d65407
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #dc8a0e
}
.hljs-strong {
  font-weight: bold;
  color: #dc8a0e
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #17ad98
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #149bda
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #796af5
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #bb60ea
}
.hljs-emphasis {
  color: #bb60ea;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #c720ca
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}