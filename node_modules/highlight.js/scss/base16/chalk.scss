pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Chalk
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme chalk
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #151515  Default Background
base01  #202020  Lighter Background (Used for status bars, line number and folding marks)
base02  #303030  Selection Background
base03  #505050  Comments, Invisibles, Line Highlighting
base04  #b0b0b0  Dark Foreground (Used for status bars)
base05  #d0d0d0  Default Foreground, Caret, Delimiters, Operators
base06  #e0e0e0  Light Foreground (Not often used)
base07  #f5f5f5  Light Background (Not often used)
base08  #fb9fb1  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #eda987  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ddb26f  Classes, Markup Bold, Search Text Background
base0B  #acc267  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #12cfc0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6fc2ef  Functions, Methods, Attribute IDs, Headings
base0E  #e1a3ee  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #deaf8f  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #d0d0d0;
  background: #151515
}
.hljs::selection,
.hljs ::selection {
  background-color: #303030;
  color: #d0d0d0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #505050 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #505050
}
/* base04 - #b0b0b0 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b0b0b0
}
/* base05 - #d0d0d0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #d0d0d0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #fb9fb1
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #eda987
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ddb26f
}
.hljs-strong {
  font-weight: bold;
  color: #ddb26f
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #acc267
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #12cfc0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6fc2ef
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #e1a3ee
}
.hljs-emphasis {
  color: #e1a3ee;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #deaf8f
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}