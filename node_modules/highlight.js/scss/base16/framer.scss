pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Framer
  Author: Framer (Maintained by <PERSON>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme framer
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #181818  Default Background
base01  #151515  Lighter Background (Used for status bars, line number and folding marks)
base02  #464646  Selection Background
base03  #747474  Comments, Invisibles, Line Highlighting
base04  #B9B9B9  Dark Foreground (Used for status bars)
base05  #D0D0D0  Default Foreground, Caret, Delimiters, Operators
base06  #E8E8E8  Light Foreground (Not often used)
base07  #EEEEEE  Light Background (Not often used)
base08  #FD886B  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #FC4769  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #FECB6E  Classes, Markup Bold, Search Text Background
base0B  #32CCDC  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #ACDDFD  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #20BCFC  Functions, Methods, Attribute IDs, Headings
base0E  #BA8CFC  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #B15F4A  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #D0D0D0;
  background: #181818
}
.hljs::selection,
.hljs ::selection {
  background-color: #464646;
  color: #D0D0D0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #747474 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #747474
}
/* base04 - #B9B9B9 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #B9B9B9
}
/* base05 - #D0D0D0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #D0D0D0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #FD886B
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #FC4769
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #FECB6E
}
.hljs-strong {
  font-weight: bold;
  color: #FECB6E
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #32CCDC
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #ACDDFD
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #20BCFC
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #BA8CFC
}
.hljs-emphasis {
  color: #BA8CFC;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #B15F4A
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}