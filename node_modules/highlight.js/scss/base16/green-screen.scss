pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Green Screen
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme green-screen
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #001100  Default Background
base01  #003300  Lighter Background (Used for status bars, line number and folding marks)
base02  #005500  Selection Background
base03  #007700  Comments, Invisibles, Line Highlighting
base04  #009900  Dark Foreground (Used for status bars)
base05  #00bb00  Default Foreground, Caret, Delimiters, Operators
base06  #00dd00  Light Foreground (Not often used)
base07  #00ff00  Light Background (Not often used)
base08  #007700  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #009900  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #007700  Classes, Markup Bold, Search Text Background
base0B  #00bb00  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #005500  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #009900  Functions, Methods, Attribute IDs, Headings
base0E  #00bb00  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #005500  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #00bb00;
  background: #001100
}
.hljs::selection,
.hljs ::selection {
  background-color: #005500;
  color: #00bb00
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #007700 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #007700
}
/* base04 - #009900 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #009900
}
/* base05 - #00bb00 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #00bb00
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #007700
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #009900
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #007700
}
.hljs-strong {
  font-weight: bold;
  color: #007700
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #00bb00
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #005500
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #009900
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #00bb00
}
.hljs-emphasis {
  color: #00bb00;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #005500
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}